"""Main ingestion script for processing staging folders and building Neo4j graph."""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

from neo4j_ingestion.config import ConfigManager, setup_logging
from neo4j_ingestion.feature_extractor import StagingProcessor
from neo4j_ingestion.graph_constructor import GraphConstructor


class Neo4jIngestionPipeline:
    """Main pipeline for Neo4j ingestion process."""
    
    def __init__(self, config_file: Optional[str] = None, staging_path: str = "data_processing_steps/staging"):
        self.config_manager = None
        self.staging_processor = None
        self.graph_constructor = None
        self.config_file = config_file
        self.staging_path = staging_path
        
    def initialize(self):
        """Initialize all components of the pipeline."""
        try:
            # Setup logging
            setup_logging()
            logging.info("Starting Neo4j ingestion pipeline initialization")
            
            # Initialize configuration
            self.config_manager = ConfigManager(self.config_file)
            logging.info("Configuration loaded successfully")
            
            # Initialize staging processor
            self.staging_processor = StagingProcessor(self.staging_path)
            self.staging_processor.initialize_extractor(self.config_manager)
            logging.info("Staging processor initialized")
            
            # Initialize graph constructor
            self.graph_constructor = GraphConstructor(self.config_manager)
            self.graph_constructor.create_indexes()
            logging.info("Graph constructor initialized and indexes created")
            
            logging.info("Pipeline initialization complete")
            
        except Exception as e:
            logging.error(f"Failed to initialize pipeline: {e}")
            raise
    
    def run_feature_extraction(self, max_workers: int = 4, save_intermediate: bool = True) -> list:
        """Run the feature extraction process on staging folders."""
        logging.info("Starting feature extraction process")
        
        try:
            # Process all staging folders
            processed_chunks = self.staging_processor.process_staging_folders(max_workers=max_workers)
            
            if not processed_chunks:
                logging.warning("No chunks were processed")
                return []
            
            # Save intermediate results if requested
            if save_intermediate:
                self.staging_processor.save_processed_chunks(processed_chunks)
            
            logging.info(f"Feature extraction complete. Processed {len(processed_chunks)} chunks")
            return processed_chunks
            
        except Exception as e:
            logging.error(f"Feature extraction failed: {e}")
            raise
    
    def run_graph_construction(self, processed_chunks: list, batch_size: int = 10) -> dict:
        """Run the graph construction process."""
        logging.info("Starting graph construction process")
        
        try:
            # Add documents to graph in batches
            results = self.graph_constructor.batch_add_documents(processed_chunks, batch_size=batch_size)
            
            # Get final statistics
            stats = self.graph_constructor.get_graph_statistics()
            
            logging.info("Graph construction complete")
            logging.info(f"Processing results: {results}")
            logging.info(f"Final graph statistics: {stats}")
            
            return {"processing_results": results, "graph_statistics": stats}
            
        except Exception as e:
            logging.error(f"Graph construction failed: {e}")
            raise
    
    def run_full_pipeline(self, max_workers: int = 4, batch_size: int = 10, 
                         save_intermediate: bool = True) -> dict:
        """Run the complete ingestion pipeline."""
        logging.info("Starting full Neo4j ingestion pipeline")
        
        try:
            # Initialize pipeline
            self.initialize()
            
            # Run feature extraction
            processed_chunks = self.run_feature_extraction(
                max_workers=max_workers, 
                save_intermediate=save_intermediate
            )
            
            if not processed_chunks:
                logging.warning("No chunks to process. Pipeline stopping.")
                return {"status": "no_data", "message": "No chunks found to process"}
            
            # Run graph construction
            graph_results = self.run_graph_construction(processed_chunks, batch_size=batch_size)
            
            logging.info("Full pipeline execution complete")
            return {
                "status": "success",
                "chunks_processed": len(processed_chunks),
                **graph_results
            }
            
        except Exception as e:
            logging.error(f"Pipeline execution failed: {e}")
            return {"status": "error", "message": str(e)}
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self.config_manager:
                self.config_manager.close_connections()
            if self.graph_constructor:
                self.graph_constructor.close()
            logging.info("Pipeline cleanup complete")
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")


def main():
    """Main entry point for the ingestion script."""
    parser = argparse.ArgumentParser(description="Neo4j Document Ingestion Pipeline")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--staging", type=str, default="data_processing_steps/staging",
                       help="Path to staging directory (default: data_processing_steps/staging)")
    parser.add_argument("--workers", type=int, default=4, 
                       help="Number of worker threads for processing (default: 4)")
    parser.add_argument("--batch-size", type=int, default=10, 
                       help="Batch size for graph operations (default: 10)")
    parser.add_argument("--no-save-intermediate", action="store_true", 
                       help="Don't save intermediate processed chunks")
    parser.add_argument("--extract-only", action="store_true", 
                       help="Only run feature extraction, skip graph construction")
    parser.add_argument("--graph-only", action="store_true", 
                       help="Only run graph construction from saved processed chunks")
    
    args = parser.parse_args()
    
    # Create pipeline
    pipeline = Neo4jIngestionPipeline(
        config_file=args.config,
        staging_path=args.staging
    )
    
    try:
        if args.extract_only:
            # Run only feature extraction
            pipeline.initialize()
            processed_chunks = pipeline.run_feature_extraction(
                max_workers=args.workers,
                save_intermediate=not args.no_save_intermediate
            )
            print(f"Feature extraction complete. Processed {len(processed_chunks)} chunks.")
            
        elif args.graph_only:
            # Run only graph construction from saved data
            import json
            processed_chunks_file = "neo4j_ingestion/processed_chunks.json"
            if not Path(processed_chunks_file).exists():
                print(f"Error: {processed_chunks_file} not found. Run feature extraction first.")
                sys.exit(1)
            
            with open(processed_chunks_file, 'r') as f:
                processed_chunks = json.load(f)
            
            pipeline.initialize()
            results = pipeline.run_graph_construction(processed_chunks, batch_size=args.batch_size)
            print(f"Graph construction complete. Results: {results}")
            
        else:
            # Run full pipeline
            results = pipeline.run_full_pipeline(
                max_workers=args.workers,
                batch_size=args.batch_size,
                save_intermediate=not args.no_save_intermediate
            )
            print(f"Pipeline execution complete. Results: {results}")
    
    except KeyboardInterrupt:
        logging.info("Pipeline interrupted by user")
        print("Pipeline interrupted by user")
    except Exception as e:
        logging.error(f"Pipeline failed: {e}")
        print(f"Pipeline failed: {e}")
        sys.exit(1)
    finally:
        pipeline.cleanup()


if __name__ == "__main__":
    main()
