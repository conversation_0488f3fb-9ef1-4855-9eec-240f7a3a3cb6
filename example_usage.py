#!/usr/bin/env python3
"""
Example usage of the PDF processing system.

This script demonstrates how to use the PDF processor programmatically.
"""

from kg_agents.pdf_processor import PDFProcessor
from kg_agents.config import config_manager
import json


def example_basic_usage():
    """Example of basic PDF processing."""
    print("=== Basic Usage Example ===")
    
    # Initialize the processor
    processor = PDFProcessor()
    
    # Process all PDFs in the raw folder
    results = processor.process_all_pdfs()
    
    # Print results
    for result in results:
        print(f"PDF: {result['pdf_name']}")
        print(f"Status: {result['status']}")
        if result['status'] == 'success':
            print(f"Chunks created: {result['chunks_created']}")
            print(f"Stored in: {result['folder_path']}")
        print("-" * 40)


def example_custom_configuration():
    """Example of using custom configuration."""
    print("=== Custom Configuration Example ===")
    
    # Configure chunking parameters
    config_manager.set_max_chunks(30)
    config_manager.set_min_tokens_per_chunk(300)
    
    # Initialize processor with custom folders
    processor = PDFProcessor(raw_folder="raw", staging_folder="staging")
    
    # Process a specific PDF (if it exists)
    try:
        # This will fail if no PDF exists, but shows the API
        result = processor.process_pdf("example.pdf")
        print(f"Processed: {result}")
    except FileNotFoundError:
        print("No example.pdf found - this is expected for the demo")


def example_inspect_chunks():
    """Example of inspecting generated chunks."""
    print("=== Chunk Inspection Example ===")
    
    import os
    from pathlib import Path
    
    staging_folder = Path("staging")
    
    # Find all chunk folders
    chunk_folders = [
        folder for folder in staging_folder.iterdir() 
        if folder.is_dir() and folder.name.startswith("chunks_")
    ]
    
    if not chunk_folders:
        print("No chunk folders found. Process some PDFs first.")
        return
    
    # Inspect the first chunk folder
    first_folder = chunk_folders[0]
    print(f"Inspecting folder: {first_folder.name}")
    
    # List all chunk files
    chunk_files = sorted([f for f in first_folder.iterdir() if f.suffix == '.json'])
    print(f"Found {len(chunk_files)} chunk files")
    
    # Show content of first chunk
    if chunk_files:
        first_chunk_path = chunk_files[0]
        with open(first_chunk_path, 'r', encoding='utf-8') as f:
            chunk_data = json.load(f)
        
        print(f"\nFirst chunk ({first_chunk_path.name}):")
        print(f"  Chunk ID: {chunk_data['chunk_id']}")
        print(f"  Token count: {chunk_data['token_count']}")
        print(f"  Text preview: {chunk_data['text'][:100]}...")


def example_chunker_only():
    """Example of using just the chunker without PDF processing."""
    print("=== Chunker Only Example ===")
    
    from kg_agents.chunker import Chunker
    
    # Sample text
    sample_text = """
    This is the first sentence of our sample document. It contains some important information.
    Here is the second sentence with more details. The third sentence adds even more context.
    This is a longer sentence that contains multiple clauses and provides extensive information about the topic we are discussing.
    Finally, this is the last sentence of our sample text.
    """
    
    # Initialize chunker
    chunker = Chunker()
    
    # Create chunks
    chunks = chunker.split_into_chunks(
        sample_text,
        max_chunks=3,
        min_tokens_per_chunk=20
    )
    
    # Display results
    print(f"Created {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i}:")
        print(f"  ID: {chunk['id']}")
        print(f"  Tokens: {chunk['token_count']}")
        print(f"  Text: {chunk['text'][:100]}...")


if __name__ == "__main__":
    print("PDF Processing System - Example Usage\n")
    
    # Run examples
    example_basic_usage()
    print("\n" + "="*60 + "\n")
    
    example_custom_configuration()
    print("\n" + "="*60 + "\n")
    
    example_inspect_chunks()
    print("\n" + "="*60 + "\n")
    
    example_chunker_only()
    
    print("\nExample completed! Check the staging folder for results.")
