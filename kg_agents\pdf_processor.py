"""PDF processing module for document analysis."""

import os
import json
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
import PyPDF2
from kg_agents.chunker import Chunker
from kg_agents.config import config_manager


class PDFProcessor:
    """Handles PDF processing, chunking, and storage."""
    
    def __init__(self, raw_folder: str = "raw", staging_folder: str = "staging"):
        """
        Initialize the PDF processor.
        
        Args:
            raw_folder: Path to the folder containing raw PDF files
            staging_folder: Path to the folder where chunks will be stored
        """
        self.raw_folder = Path(raw_folder)
        self.staging_folder = Path(staging_folder)
        self.chunker = Chunker()
        
        # Ensure folders exist
        self.raw_folder.mkdir(exist_ok=True)
        self.staging_folder.mkdir(exist_ok=True)
        
        # Counter for unique folder naming
        self._folder_counter = self._get_next_folder_counter()
    
    def _get_next_folder_counter(self) -> int:
        """Get the next available folder counter."""
        existing_folders = [
            folder for folder in self.staging_folder.iterdir() 
            if folder.is_dir() and folder.name.startswith("chunks_")
        ]
        
        if not existing_folders:
            return 1
        
        # Extract numbers from existing folder names
        numbers = []
        for folder in existing_folders:
            try:
                # Extract number from "chunks_n_name_files" format
                parts = folder.name.split("_")
                if len(parts) >= 2 and parts[0] == "chunks":
                    numbers.append(int(parts[1]))
            except (ValueError, IndexError):
                continue
        
        return max(numbers) + 1 if numbers else 1
    
    def extract_text_from_pdf(self, pdf_path: Path) -> str:
        """
        Extract text from a PDF file.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text as a string
        """
        text = ""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
            raise
        
        return text.strip()
    
    def create_chunks_folder(self, pdf_name: str) -> Path:
        """
        Create a folder for storing chunks with the format: chunks_n_name_files
        
        Args:
            pdf_name: Name of the PDF file (without extension)
            
        Returns:
            Path to the created folder
        """
        folder_name = f"chunks_{self._folder_counter}_{pdf_name}_files"
        folder_path = self.staging_folder / folder_name
        folder_path.mkdir(exist_ok=True)
        
        self._folder_counter += 1
        return folder_path
    
    def save_chunk(self, chunk: Dict[str, Any], chunk_folder: Path, chunk_index: int) -> Path:
        """
        Save a chunk as JSON file with the format: chunk_n.json
        
        Args:
            chunk: The chunk data to save
            chunk_folder: Folder where the chunk should be saved
            chunk_index: Index of the chunk
            
        Returns:
            Path to the saved chunk file
        """
        chunk_filename = f"chunk_{chunk_index}.json"
        chunk_path = chunk_folder / chunk_filename
        
        # Add metadata to the chunk
        chunk_data = {
            "chunk_index": chunk_index,
            "chunk_id": chunk["id"],
            "text": chunk["text"],
            "token_count": chunk["token_count"],
            "created_at": str(uuid.uuid4()),  # Using UUID as timestamp alternative
        }
        
        with open(chunk_path, 'w', encoding='utf-8') as f:
            json.dump(chunk_data, f, indent=2, ensure_ascii=False)
        
        return chunk_path
    
    def process_pdf(self, pdf_filename: str) -> Dict[str, Any]:
        """
        Process a single PDF file: extract text, chunk it, and save chunks.
        
        Args:
            pdf_filename: Name of the PDF file in the raw folder
            
        Returns:
            Dictionary with processing results
        """
        pdf_path = self.raw_folder / pdf_filename
        
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Extract PDF name without extension
        pdf_name = pdf_path.stem
        
        print(f"Processing PDF: {pdf_filename}")
        
        # Extract text from PDF
        text = self.extract_text_from_pdf(pdf_path)
        
        if not text.strip():
            print(f"Warning: No text extracted from {pdf_filename}")
            return {
                "pdf_name": pdf_name,
                "status": "no_text",
                "chunks_created": 0,
                "folder_path": None
            }
        
        # Create chunks
        chunks = self.chunker.split_into_chunks(
            text,
            max_chunks=config_manager.get_max_chunks(),
            min_tokens_per_chunk=config_manager.get_min_tokens_per_chunk()
        )
        
        # Create folder for chunks
        chunks_folder = self.create_chunks_folder(pdf_name)
        
        # Save each chunk
        saved_chunks = []
        for i, chunk in enumerate(chunks):
            chunk_path = self.save_chunk(chunk, chunks_folder, i)
            saved_chunks.append(str(chunk_path))
        
        result = {
            "pdf_name": pdf_name,
            "status": "success",
            "chunks_created": len(chunks),
            "folder_path": str(chunks_folder),
            "chunk_files": saved_chunks
        }
        
        print(f"Successfully processed {pdf_filename}: {len(chunks)} chunks created in {chunks_folder}")
        return result
    
    def process_all_pdfs(self) -> List[Dict[str, Any]]:
        """
        Process all PDF files in the raw folder.
        
        Returns:
            List of processing results for each PDF
        """
        pdf_files = [f for f in self.raw_folder.iterdir() if f.suffix.lower() == '.pdf']
        
        if not pdf_files:
            print("No PDF files found in the raw folder.")
            return []
        
        results = []
        for pdf_file in pdf_files:
            try:
                result = self.process_pdf(pdf_file.name)
                results.append(result)
            except Exception as e:
                print(f"Error processing {pdf_file.name}: {e}")
                results.append({
                    "pdf_name": pdf_file.stem,
                    "status": "error",
                    "error": str(e),
                    "chunks_created": 0,
                    "folder_path": None
                })
        
        return results
