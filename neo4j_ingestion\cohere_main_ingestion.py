"""Enhanced main ingestion script with Cohere embedding support."""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

from neo4j_ingestion.config import ConfigManager, setup_logging
from neo4j_ingestion.cohere_feature_extractor import CohereStagingProcessor
from neo4j_ingestion.graph_constructor import GraphConstructor


class CohereNeo4jIngestionPipeline:
    """Enhanced pipeline for Neo4j ingestion with Cohere embedding support."""
    
    def __init__(self, config_file: Optional[str] = None, 
                 staging_path: str = "data_processing_steps/staging",
                 use_batch_embeddings: bool = True,
                 embedding_provider: str = "cohere"):
        self.config_manager = None
        self.staging_processor = None
        self.graph_constructor = None
        self.config_file = config_file
        self.staging_path = staging_path
        self.use_batch_embeddings = use_batch_embeddings
        self.embedding_provider = embedding_provider  # "cohere" or "openai"
        
    def initialize(self):
        """Initialize all components of the pipeline."""
        try:
            # Setup logging
            setup_logging()
            logging.info("Starting Cohere-enabled Neo4j ingestion pipeline initialization")
            
            # Initialize configuration
            self.config_manager = ConfigManager(self.config_file)
            logging.info("Configuration loaded successfully")
            
            # Check if Cohere is available and configured
            if self.embedding_provider == "cohere":
                try:
                    cohere_client = self.config_manager.get_cohere_client()
                    logging.info("Cohere client initialized successfully")
                except Exception as e:
                    logging.warning(f"Cohere not available: {e}")
                    logging.info("Falling back to OpenAI embeddings")
                    self.embedding_provider = "openai"
                    self.use_batch_embeddings = False
            
            # Initialize staging processor based on provider
            if self.embedding_provider == "cohere":
                self.staging_processor = CohereStagingProcessor(
                    self.staging_path, 
                    use_batch_embeddings=self.use_batch_embeddings
                )
                self.staging_processor.initialize(self.config_manager)
                logging.info(f"Cohere staging processor initialized (batch: {self.use_batch_embeddings})")
            else:
                # Fall back to original processor for OpenAI
                from neo4j_ingestion.feature_extractor import StagingProcessor
                self.staging_processor = StagingProcessor(self.staging_path)
                self.staging_processor.initialize_extractor(self.config_manager)
                logging.info("OpenAI staging processor initialized")
            
            # Initialize graph constructor
            self.graph_constructor = GraphConstructor(self.config_manager)
            self.graph_constructor.create_indexes()
            logging.info("Graph constructor initialized and indexes created")
            
            logging.info("Pipeline initialization complete")
            
        except Exception as e:
            logging.error(f"Failed to initialize pipeline: {e}")
            raise
    
    def run_feature_extraction(self, max_workers: int = 4, save_intermediate: bool = True) -> list:
        """Run the feature extraction process on staging folders."""
        logging.info(f"Starting feature extraction process with {self.embedding_provider} embeddings")
        
        try:
            # Process all staging folders
            processed_chunks = self.staging_processor.process_staging_folders(max_workers=max_workers)
            
            if not processed_chunks:
                logging.warning("No chunks were processed")
                return []
            
            # Save intermediate results if requested
            if save_intermediate:
                output_file = f"neo4j_ingestion/{self.embedding_provider}_processed_chunks.json"
                self.staging_processor.save_processed_chunks(processed_chunks, output_file)
            
            logging.info(f"Feature extraction complete. Processed {len(processed_chunks)} chunks")
            return processed_chunks
            
        except Exception as e:
            logging.error(f"Feature extraction failed: {e}")
            raise
    
    def run_graph_construction(self, processed_chunks: list, batch_size: int = 10) -> dict:
        """Run the graph construction process."""
        logging.info("Starting graph construction process")
        
        try:
            # Add documents to graph in batches
            results = self.graph_constructor.batch_add_documents(processed_chunks, batch_size=batch_size)
            
            # Get final statistics
            stats = self.graph_constructor.get_graph_statistics()
            
            logging.info("Graph construction complete")
            logging.info(f"Processing results: {results}")
            logging.info(f"Final graph statistics: {stats}")
            
            return {"processing_results": results, "graph_statistics": stats}
            
        except Exception as e:
            logging.error(f"Graph construction failed: {e}")
            raise
    
    def run_full_pipeline(self, max_workers: int = 4, batch_size: int = 10, 
                         save_intermediate: bool = True) -> dict:
        """Run the complete ingestion pipeline."""
        logging.info("Starting full Cohere-enabled Neo4j ingestion pipeline")
        
        try:
            # Initialize pipeline
            self.initialize()
            
            # Run feature extraction
            processed_chunks = self.run_feature_extraction(
                max_workers=max_workers, 
                save_intermediate=save_intermediate
            )
            
            if not processed_chunks:
                logging.warning("No chunks to process. Pipeline stopping.")
                return {"status": "no_data", "message": "No chunks found to process"}
            
            # Run graph construction
            graph_results = self.run_graph_construction(processed_chunks, batch_size=batch_size)
            
            logging.info("Full pipeline execution complete")
            return {
                "status": "success",
                "chunks_processed": len(processed_chunks),
                "embedding_provider": self.embedding_provider,
                "batch_embeddings": self.use_batch_embeddings,
                **graph_results
            }
            
        except Exception as e:
            logging.error(f"Pipeline execution failed: {e}")
            return {"status": "error", "message": str(e)}
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self.config_manager:
                # Note: ConfigManager doesn't have close_connections method in current implementation
                pass
            if self.graph_constructor:
                self.graph_constructor.close()
            logging.info("Pipeline cleanup complete")
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")


def main():
    """Main entry point for the Cohere-enabled ingestion script."""
    parser = argparse.ArgumentParser(description="Cohere-enabled Neo4j Document Ingestion Pipeline")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--staging", type=str, default="data_processing_steps/staging",
                       help="Path to staging directory (default: data_processing_steps/staging)")
    parser.add_argument("--workers", type=int, default=4, 
                       help="Number of worker threads for processing (default: 4)")
    parser.add_argument("--batch-size", type=int, default=10,
                       help="Batch size for graph operations (default: 10)")
    parser.add_argument("--no-save-intermediate", action="store_true",
                       help="Don't save intermediate processing results")
    parser.add_argument("--extract-only", action="store_true",
                       help="Only run feature extraction, skip graph construction")
    parser.add_argument("--graph-only", action="store_true",
                       help="Only run graph construction (requires existing processed chunks)")
    parser.add_argument("--embedding-provider", type=str, choices=["cohere", "openai"], 
                       default="cohere", help="Embedding provider to use (default: cohere)")
    parser.add_argument("--no-batch-embeddings", action="store_true",
                       help="Use individual embeddings instead of batch processing (only for Cohere)")
    
    args = parser.parse_args()
    
    # Create pipeline
    pipeline = CohereNeo4jIngestionPipeline(
        config_file=args.config,
        staging_path=args.staging,
        use_batch_embeddings=not args.no_batch_embeddings,
        embedding_provider=args.embedding_provider
    )
    
    try:
        if args.extract_only:
            # Run only feature extraction
            pipeline.initialize()
            processed_chunks = pipeline.run_feature_extraction(
                max_workers=args.workers,
                save_intermediate=not args.no_save_intermediate
            )
            print(f"✅ Feature extraction complete. Processed {len(processed_chunks)} chunks")
            
        elif args.graph_only:
            # Run only graph construction
            pipeline.initialize()
            
            # Load previously processed chunks
            import json
            processed_file = f"neo4j_ingestion/{args.embedding_provider}_processed_chunks.json"
            try:
                with open(processed_file, 'r', encoding='utf-8') as f:
                    processed_chunks = json.load(f)
                print(f"📁 Loaded {len(processed_chunks)} processed chunks from {processed_file}")
            except FileNotFoundError:
                print(f"❌ Processed chunks file not found: {processed_file}")
                print("Run feature extraction first or use --extract-only")
                sys.exit(1)
            
            graph_results = pipeline.run_graph_construction(
                processed_chunks, 
                batch_size=args.batch_size
            )
            print("✅ Graph construction complete")
            print(f"📊 Results: {graph_results}")
            
        else:
            # Run full pipeline
            results = pipeline.run_full_pipeline(
                max_workers=args.workers,
                batch_size=args.batch_size,
                save_intermediate=not args.no_save_intermediate
            )
            
            if results["status"] == "success":
                print("✅ Pipeline execution successful")
                print(f"📊 Processed {results['chunks_processed']} chunks")
                print(f"🔧 Embedding provider: {results['embedding_provider']}")
                print(f"📦 Batch embeddings: {results['batch_embeddings']}")
                print(f"📈 Graph statistics: {results['graph_statistics']}")
            else:
                print(f"❌ Pipeline failed: {results.get('message', 'Unknown error')}")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n⚠️ Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Pipeline failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
