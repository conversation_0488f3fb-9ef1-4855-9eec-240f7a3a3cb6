"""
Main workflow script for Cohere-enabled document analysis and Neo4j ingestion.

This script orchestrates the complete workflow:
1. Check staging folder for data
2. Process data through Cohere batch embedding API
3. Ingest results into Neo4j database

Usage:
    python cohere_workflow.py --config neo4j_ingestion/config.json
"""

import argparse
import logging
import sys
import json
from pathlib import Path
from typing import Dict, Any

from neo4j_ingestion.config import ConfigManager, setup_logging
from neo4j_ingestion.cohere_main_ingestion import CohereNeo4jIngestionPipeline


def check_staging_data(staging_path: str) -> Dict[str, Any]:
    """
    Check if there is data available in the staging folder.
    
    Args:
        staging_path: Path to the staging directory
        
    Returns:
        Dictionary with information about available data
    """
    staging_dir = Path(staging_path)
    
    if not staging_dir.exists():
        return {
            "has_data": False,
            "message": f"Staging directory does not exist: {staging_path}",
            "folders": []
        }
    
    folders = []
    for item in staging_dir.iterdir():
        if item.is_dir():
            json_files = list(item.glob("*.json"))
            folders.append({
                "name": item.name,
                "path": str(item),
                "json_files": len(json_files),
                "files": [f.name for f in json_files[:5]]  # Show first 5 files
            })
    
    return {
        "has_data": len(folders) > 0,
        "message": f"Found {len(folders)} folders in staging directory",
        "folders": folders,
        "total_folders": len(folders)
    }


def display_staging_info(staging_info: Dict[str, Any]):
    """Display information about staging data."""
    print("\n" + "="*60)
    print("📁 STAGING DATA ANALYSIS")
    print("="*60)
    
    if not staging_info["has_data"]:
        print(f"❌ {staging_info['message']}")
        return
    
    print(f"✅ {staging_info['message']}")
    print(f"\n📊 Found {staging_info['total_folders']} folders:")
    
    for i, folder in enumerate(staging_info["folders"]):
        print(f"\n  [{i}] {folder['name']}")
        print(f"      📄 JSON files: {folder['json_files']}")
        if folder['files']:
            print(f"      📝 Sample files: {', '.join(folder['files'])}")
            if folder['json_files'] > 5:
                print(f"      ... and {folder['json_files'] - 5} more files")


def check_configuration(config_file: str) -> Dict[str, Any]:
    """
    Check if the configuration is properly set up for Cohere.
    
    Args:
        config_file: Path to the configuration file
        
    Returns:
        Dictionary with configuration status
    """
    try:
        config_manager = ConfigManager(config_file)
        
        # Check Cohere configuration
        cohere_available = False
        cohere_error = None
        try:
            cohere_client = config_manager.get_cohere_client()
            cohere_available = True
        except Exception as e:
            cohere_error = str(e)
        
        # Check OpenAI configuration (fallback)
        openai_available = False
        openai_error = None
        try:
            openai_client = config_manager.get_openai_client()
            openai_available = True
        except Exception as e:
            openai_error = str(e)
        
        # Check Neo4j configuration
        neo4j_available = False
        neo4j_error = None
        try:
            neo4j_driver = config_manager.get_neo4j_driver()
            neo4j_available = True
        except Exception as e:
            neo4j_error = str(e)
        
        return {
            "config_loaded": True,
            "cohere": {
                "available": cohere_available,
                "error": cohere_error,
                "config": {
                    "environment": config_manager.cohere_config.environment if cohere_available else None,
                    "client_name": config_manager.cohere_config.client_name if cohere_available else None,
                    "model": config_manager.cohere_config.embedding_model if cohere_available else None
                }
            },
            "openai": {
                "available": openai_available,
                "error": openai_error
            },
            "neo4j": {
                "available": neo4j_available,
                "error": neo4j_error
            }
        }
        
    except Exception as e:
        return {
            "config_loaded": False,
            "error": str(e)
        }


def display_configuration_info(config_info: Dict[str, Any]):
    """Display configuration information."""
    print("\n" + "="*60)
    print("⚙️  CONFIGURATION STATUS")
    print("="*60)
    
    if not config_info["config_loaded"]:
        print(f"❌ Configuration failed to load: {config_info['error']}")
        return
    
    print("✅ Configuration loaded successfully")
    
    # Cohere status
    print(f"\n🔧 Cohere API:")
    if config_info["cohere"]["available"]:
        print("   ✅ Available")
        cohere_config = config_info["cohere"]["config"]
        print(f"   🌍 Environment: {cohere_config['environment']}")
        print(f"   🏷️  Client Name: {cohere_config['client_name']}")
        print(f"   🤖 Model: {cohere_config['model']}")
    else:
        print(f"   ❌ Not available: {config_info['cohere']['error']}")
    
    # OpenAI status (fallback)
    print(f"\n🔧 OpenAI API (fallback):")
    if config_info["openai"]["available"]:
        print("   ✅ Available")
    else:
        print(f"   ❌ Not available: {config_info['openai']['error']}")
    
    # Neo4j status
    print(f"\n🔧 Neo4j Database:")
    if config_info["neo4j"]["available"]:
        print("   ✅ Available")
    else:
        print(f"   ❌ Not available: {config_info['neo4j']['error']}")


def run_workflow(config_file: str, staging_path: str, 
                embedding_provider: str = "cohere",
                use_batch_embeddings: bool = True,
                max_workers: int = 4,
                batch_size: int = 10,
                dry_run: bool = False) -> Dict[str, Any]:
    """
    Run the complete Cohere workflow.
    
    Args:
        config_file: Path to configuration file
        staging_path: Path to staging directory
        embedding_provider: "cohere" or "openai"
        use_batch_embeddings: Whether to use batch embeddings (Cohere only)
        max_workers: Number of worker threads
        batch_size: Batch size for Neo4j operations
        dry_run: If True, only check configuration and data without processing
        
    Returns:
        Dictionary with workflow results
    """
    print("\n" + "="*60)
    print("🚀 STARTING COHERE WORKFLOW")
    print("="*60)
    
    # Step 1: Check staging data
    print("\n📋 Step 1: Checking staging data...")
    staging_info = check_staging_data(staging_path)
    display_staging_info(staging_info)
    
    if not staging_info["has_data"]:
        return {
            "status": "no_data",
            "message": "No data found in staging directory",
            "staging_info": staging_info
        }
    
    # Step 2: Check configuration
    print("\n📋 Step 2: Checking configuration...")
    config_info = check_configuration(config_file)
    display_configuration_info(config_info)
    
    if not config_info["config_loaded"]:
        return {
            "status": "config_error",
            "message": "Configuration failed to load",
            "config_info": config_info
        }
    
    # Check if preferred embedding provider is available
    if embedding_provider == "cohere" and not config_info["cohere"]["available"]:
        if config_info["openai"]["available"]:
            print("\n⚠️  Cohere not available, falling back to OpenAI")
            embedding_provider = "openai"
            use_batch_embeddings = False
        else:
            return {
                "status": "no_embedding_provider",
                "message": "Neither Cohere nor OpenAI is available",
                "config_info": config_info
            }
    
    if not config_info["neo4j"]["available"]:
        return {
            "status": "neo4j_error",
            "message": "Neo4j database is not available",
            "config_info": config_info
        }
    
    if dry_run:
        print("\n🔍 DRY RUN: Configuration and data checks completed successfully")
        return {
            "status": "dry_run_success",
            "message": "Dry run completed successfully",
            "staging_info": staging_info,
            "config_info": config_info,
            "embedding_provider": embedding_provider,
            "use_batch_embeddings": use_batch_embeddings
        }
    
    # Step 3: Run the pipeline
    print(f"\n📋 Step 3: Running pipeline with {embedding_provider} embeddings...")
    print(f"   🔧 Batch embeddings: {use_batch_embeddings}")
    print(f"   👥 Workers: {max_workers}")
    print(f"   📦 Batch size: {batch_size}")
    
    try:
        pipeline = CohereNeo4jIngestionPipeline(
            config_file=config_file,
            staging_path=staging_path,
            use_batch_embeddings=use_batch_embeddings,
            embedding_provider=embedding_provider
        )
        
        results = pipeline.run_full_pipeline(
            max_workers=max_workers,
            batch_size=batch_size,
            save_intermediate=True
        )
        
        return {
            "status": "success",
            "message": "Workflow completed successfully",
            "staging_info": staging_info,
            "config_info": config_info,
            "pipeline_results": results
        }
        
    except Exception as e:
        return {
            "status": "pipeline_error",
            "message": f"Pipeline execution failed: {e}",
            "staging_info": staging_info,
            "config_info": config_info,
            "error": str(e)
        }


def main():
    """Main entry point for the Cohere workflow script."""
    parser = argparse.ArgumentParser(
        description="Cohere-enabled document analysis and Neo4j ingestion workflow",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with default settings
  python cohere_workflow.py --config neo4j_ingestion/config.json
  
  # Dry run to check configuration and data
  python cohere_workflow.py --config neo4j_ingestion/config.json --dry-run
  
  # Use OpenAI instead of Cohere
  python cohere_workflow.py --config neo4j_ingestion/config.json --embedding-provider openai
  
  # Use individual embeddings instead of batch
  python cohere_workflow.py --config neo4j_ingestion/config.json --no-batch-embeddings
        """
    )
    
    parser.add_argument("--config", type=str, required=True,
                       help="Path to configuration file (required)")
    parser.add_argument("--staging", type=str, default="data_processing_steps/staging",
                       help="Path to staging directory (default: data_processing_steps/staging)")
    parser.add_argument("--embedding-provider", type=str, choices=["cohere", "openai"], 
                       default="cohere", help="Embedding provider to use (default: cohere)")
    parser.add_argument("--no-batch-embeddings", action="store_true",
                       help="Use individual embeddings instead of batch processing")
    parser.add_argument("--workers", type=int, default=4,
                       help="Number of worker threads (default: 4)")
    parser.add_argument("--batch-size", type=int, default=10,
                       help="Batch size for Neo4j operations (default: 10)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Check configuration and data without processing")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(log_level)
    
    try:
        results = run_workflow(
            config_file=args.config,
            staging_path=args.staging,
            embedding_provider=args.embedding_provider,
            use_batch_embeddings=not args.no_batch_embeddings,
            max_workers=args.workers,
            batch_size=args.batch_size,
            dry_run=args.dry_run
        )
        
        # Display final results
        print("\n" + "="*60)
        print("📊 WORKFLOW RESULTS")
        print("="*60)
        
        if results["status"] == "success":
            print("✅ Workflow completed successfully!")
            pipeline_results = results["pipeline_results"]
            print(f"📈 Processed {pipeline_results['chunks_processed']} chunks")
            print(f"🔧 Embedding provider: {pipeline_results['embedding_provider']}")
            print(f"📦 Batch embeddings: {pipeline_results['batch_embeddings']}")
            if 'graph_statistics' in pipeline_results:
                print(f"📊 Graph statistics: {pipeline_results['graph_statistics']}")
        
        elif results["status"] == "dry_run_success":
            print("✅ Dry run completed successfully!")
            print("🔧 Configuration and data checks passed")
            print(f"📊 Ready to process {results['staging_info']['total_folders']} folders")
            print(f"🚀 Run without --dry-run to execute the pipeline")
        
        else:
            print(f"❌ Workflow failed: {results['message']}")
            if args.verbose and 'error' in results:
                print(f"🔍 Error details: {results['error']}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Workflow interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Workflow failed with unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
