# Cohere API Integration for Document Analysis

This document describes the Cohere API integration for embeddings in the GD302 document analysis project.

## Overview

The integration replaces OpenAI embeddings with Cohere's batch embedding API, providing:

1. **Batch Processing**: Efficient processing of multiple documents through Cohere's batch embedding jobs API
2. **Cost Optimization**: Batch processing typically offers better pricing than individual API calls
3. **Scalability**: Handle large document collections efficiently
4. **Fallback Support**: Automatic fallback to OpenAI if Cohere is unavailable

## Features

- ✅ Cohere ClientV2 integration with TEST environment and GD302 client name
- ✅ Batch embedding jobs API support
- ✅ JSONL dataset creation and upload
- ✅ Automatic job monitoring and result retrieval
- ✅ Neo4j integration with Cohere embeddings
- ✅ Fallback to OpenAI embeddings if Cohere unavailable
- ✅ Individual embedding support for smaller datasets
- ✅ Comprehensive error handling and logging

## Installation

1. **Install Dependencies**:
   ```bash
   pip install cohere>=5.16.0 pandas>=2.0.0
   ```

2. **Update Configuration**:
   Copy the updated configuration template:
   ```bash
   cp neo4j_ingestion/config_template.json neo4j_ingestion/config.json
   ```

3. **Add API Keys**:
   Edit `neo4j_ingestion/config.json` and add your Cohere API key:
   ```json
   {
     "cohere": {
       "api_key": "your_cohere_api_key_here",
       "environment": "TEST",
       "client_name": "GD302",
       "embedding_model": "embed-english-v3.0",
       "input_type": "search_document",
       "embedding_types": ["float"],
       "truncate": "END"
     }
   }
   ```

## Configuration

### Cohere Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `api_key` | Your Cohere API key | Required |
| `environment` | Cohere environment | "TEST" |
| `client_name` | Client identifier | "GD302" |
| `embedding_model` | Embedding model to use | "embed-english-v3.0" |
| `input_type` | Type of input text | "search_document" |
| `embedding_types` | Types of embeddings to generate | ["float"] |
| `truncate` | Truncation strategy | "END" |

### Environment Variables

You can also configure using environment variables:

```bash
export COHERE_API_KEY="your_cohere_api_key"
export COHERE_ENVIRONMENT="TEST"
export COHERE_CLIENT_NAME="GD302"
export COHERE_EMBEDDING_MODEL="embed-english-v3.0"
```

## Usage

### 1. Main Workflow Script

The easiest way to use the Cohere integration:

```bash
# Basic usage with Cohere batch embeddings
python cohere_workflow.py --config neo4j_ingestion/config.json

# Dry run to check configuration and data
python cohere_workflow.py --config neo4j_ingestion/config.json --dry-run

# Use individual embeddings instead of batch
python cohere_workflow.py --config neo4j_ingestion/config.json --no-batch-embeddings

# Use OpenAI as fallback
python cohere_workflow.py --config neo4j_ingestion/config.json --embedding-provider openai
```

### 2. Enhanced Pipeline

Use the enhanced pipeline directly:

```bash
# Run with Cohere batch embeddings
python -m neo4j_ingestion.cohere_main_ingestion --config neo4j_ingestion/config.json

# Run with individual Cohere embeddings
python -m neo4j_ingestion.cohere_main_ingestion --config neo4j_ingestion/config.json --no-batch-embeddings

# Extract features only
python -m neo4j_ingestion.cohere_main_ingestion --config neo4j_ingestion/config.json --extract-only

# Graph construction only (requires existing processed chunks)
python -m neo4j_ingestion.cohere_main_ingestion --config neo4j_ingestion/config.json --graph-only
```

### 3. Programmatic Usage

```python
from neo4j_ingestion.cohere_main_ingestion import CohereNeo4jIngestionPipeline

# Create pipeline with Cohere batch embeddings
pipeline = CohereNeo4jIngestionPipeline(
    config_file="neo4j_ingestion/config.json",
    staging_path="data_processing_steps/staging",
    use_batch_embeddings=True,
    embedding_provider="cohere"
)

# Run full pipeline
results = pipeline.run_full_pipeline(
    max_workers=4,
    batch_size=10,
    save_intermediate=True
)

print(f"Processed {results['chunks_processed']} chunks")
print(f"Embedding provider: {results['embedding_provider']}")
```

## Workflow Steps

The Cohere integration follows this workflow:

1. **Data Discovery**: Scan `data_processing_steps/staging` for folders with JSON chunks
2. **Data Preparation**: Extract text from JSON files and create JSONL dataset
3. **Dataset Upload**: Upload JSONL to Cohere for batch processing
4. **Job Submission**: Create and submit embedding job to Cohere
5. **Job Monitoring**: Wait for job completion
6. **Result Retrieval**: Download embeddings and parse results
7. **Neo4j Ingestion**: Store documents and embeddings in Neo4j graph database

## Data Structure

### Input JSON Format

Each chunk JSON file should contain:

```json
{
  "text": "Your document text here...",
  "metadata": {
    "source": "document_name",
    "topic": "document_topic"
  },
  "chunk_id": "chunk_0",
  "chunk_index": 0
}
```

### Output Format

Processed chunks include:

```json
{
  "chunk_id": "folder_name_chunk_0",
  "text": "Your document text here...",
  "embedding": [0.1, 0.2, 0.3, ...],
  "key_phrases": ["phrase1", "phrase2", ...],
  "tokens_frequency": {"word1": 5, "word2": 3},
  "metadata": {...},
  "embedding_provider": "cohere_batch",
  "source_folder": "chunks_0_document_name"
}
```

## Testing

Run the integration tests:

```bash
python test_cohere_integration.py
```

This will:
- ✅ Test configuration loading
- ✅ Test data processing components
- ✅ Test Cohere service structure
- ✅ Test workflow in dry-run mode
- 🧹 Clean up test data

## Error Handling

The integration includes comprehensive error handling:

1. **API Key Validation**: Checks for valid Cohere API key
2. **Fallback Support**: Automatically falls back to OpenAI if Cohere unavailable
3. **Job Monitoring**: Handles job failures and timeouts
4. **Data Validation**: Validates input data and handles missing fields
5. **Network Resilience**: Retries on network errors

## Performance Considerations

### Batch vs Individual Embeddings

| Method | Best For | Pros | Cons |
|--------|----------|------|------|
| Batch | Large datasets (100+ chunks) | Cost-effective, efficient | Longer initial wait time |
| Individual | Small datasets, real-time | Immediate results | Higher cost per embedding |

### Optimization Tips

1. **Batch Size**: Use batch embeddings for datasets with 50+ chunks
2. **Workers**: Adjust `--workers` based on your system (default: 4)
3. **Neo4j Batch Size**: Adjust `--batch-size` for optimal Neo4j performance (default: 10)
4. **Memory**: Large embeddings may require memory management for huge datasets

## Troubleshooting

### Common Issues

1. **"Cohere API key not found"**
   - Add your API key to `neo4j_ingestion/config.json`
   - Or set `COHERE_API_KEY` environment variable

2. **"No data found in staging directory"**
   - Ensure JSON files are in `data_processing_steps/staging/`
   - Check folder structure: `staging/folder_name/*.json`

3. **"Job failed" or timeout errors**
   - Check Cohere API status
   - Verify your API quota and limits
   - Try with smaller datasets first

4. **Neo4j connection errors**
   - Ensure Neo4j is running
   - Check credentials in config file
   - Verify database is accessible

### Debug Mode

Enable verbose logging:

```bash
python cohere_workflow.py --config neo4j_ingestion/config.json --verbose
```

## Integration Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Staging       │    │   Cohere Batch   │    │   Neo4j Graph   │
│   JSON Files    │───▶│   Embedding API  │───▶│   Database      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Text Extraction │    │ JSONL Dataset    │    │ Document Nodes  │
│ & Validation    │    │ Upload & Jobs    │    │ + Embeddings    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Next Steps

1. **Add Real API Keys**: Update configuration with your actual Cohere API key
2. **Prepare Data**: Place your JSON chunk files in the staging directory
3. **Start Neo4j**: Ensure Neo4j database is running and accessible
4. **Run Workflow**: Execute the complete workflow with your data
5. **Monitor Results**: Check logs and Neo4j for successful ingestion

For more detailed examples and advanced usage, see the example files in the `neo4j_ingestion/` directory.
