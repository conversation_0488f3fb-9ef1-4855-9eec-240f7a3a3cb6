"""Configuration management for the document analysis system."""

import os
from typing import Optional


class ConfigManager:
    """Manages configuration settings for the document analysis system."""
    
    def __init__(self):
        self.tokenizer_name = os.getenv('TOKENIZER_NAME', 'cl100k_base')
        self.max_chunks = int(os.getenv('MAX_CHUNKS', '45'))
        self.min_tokens_per_chunk = int(os.getenv('MIN_TOKENS_PER_CHUNK', '500'))
        
    def get_tokenizer_name(self) -> str:
        """Get the tokenizer name."""
        return self.tokenizer_name
    
    def get_max_chunks(self) -> int:
        """Get the maximum number of chunks."""
        return self.max_chunks
    
    def get_min_tokens_per_chunk(self) -> int:
        """Get the minimum tokens per chunk."""
        return self.min_tokens_per_chunk
    
    def set_tokenizer_name(self, name: str) -> None:
        """Set the tokenizer name."""
        self.tokenizer_name = name
    
    def set_max_chunks(self, max_chunks: int) -> None:
        """Set the maximum number of chunks."""
        self.max_chunks = max_chunks
    
    def set_min_tokens_per_chunk(self, min_tokens: int) -> None:
        """Set the minimum tokens per chunk."""
        self.min_tokens_per_chunk = min_tokens


# Global configuration manager instance
config_manager = ConfigManager()
