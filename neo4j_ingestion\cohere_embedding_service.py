"""Cohere embedding service for batch processing document chunks."""

import os
import json
import logging
import tempfile
from typing import List, Dict, Any, Optional
from pathlib import Path
import cohere
from neo4j_ingestion.config import ConfigManager


class CohereEmbeddingService:
    """Service for handling Cohere batch embedding jobs."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.cohere_client = config_manager.get_cohere_client()
        self.cohere_config = config_manager.cohere_config
        
    def create_jsonl_dataset(self, chunks_data: List[Dict[str, Any]], 
                           output_path: Optional[str] = None) -> str:
        """
        Create a JSONL file from chunks data for Cohere embedding jobs.
        
        Args:
            chunks_data: List of chunk dictionaries containing 'text' field
            output_path: Optional path for output file. If None, creates temp file.
            
        Returns:
            Path to the created JSONL file
        """
        if output_path is None:
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False)
            output_path = temp_file.name
            temp_file.close()
        
        logging.info(f"Creating JSONL dataset with {len(chunks_data)} chunks")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, chunk in enumerate(chunks_data):
                # Extract text and create JSONL entry
                text = chunk.get('text', '')
                if not text:
                    logging.warning(f"Chunk {i} has no text content, skipping")
                    continue
                
                # Create JSONL entry with required fields
                jsonl_entry = {
                    'text': text,
                    'chunk_id': chunk.get('chunk_id', f'chunk_{i}'),
                    'metadata': chunk.get('metadata', {})
                }
                
                f.write(json.dumps(jsonl_entry) + '\n')
        
        logging.info(f"JSONL dataset created at: {output_path}")
        return output_path
    
    def upload_dataset(self, jsonl_file_path: str, dataset_name: str) -> cohere.Dataset:
        """
        Upload a JSONL dataset to Cohere for embedding jobs.
        
        Args:
            jsonl_file_path: Path to the JSONL file
            dataset_name: Name for the dataset
            
        Returns:
            Cohere Dataset object
        """
        logging.info(f"Uploading dataset: {dataset_name}")
        
        try:
            with open(jsonl_file_path, 'rb') as f:
                dataset = self.cohere_client.datasets.create(
                    name=dataset_name,
                    data=f,
                    keep_fields=['text'],  # Only keep the text field for embedding
                    optional_fields=['chunk_id', 'metadata'],  # Keep these as optional
                    type="embed-input"
                )
            
            # Wait for dataset validation
            logging.info("Waiting for dataset validation...")
            validated_dataset = self.cohere_client.wait(dataset)
            logging.info(f"Dataset uploaded and validated: {validated_dataset.id}")
            
            return validated_dataset
            
        except Exception as e:
            logging.error(f"Failed to upload dataset: {e}")
            raise
    
    def create_embedding_job(self, dataset_id: str) -> cohere.EmbedJob:
        """
        Create and submit an embedding job to Cohere.
        
        Args:
            dataset_id: ID of the uploaded dataset
            
        Returns:
            Cohere EmbedJob object
        """
        logging.info(f"Creating embedding job for dataset: {dataset_id}")
        
        try:
            embed_job_response = self.cohere_client.embed_jobs.create(
                dataset_id=dataset_id,
                input_type=self.cohere_config.input_type,
                model=self.cohere_config.embedding_model,
                embedding_types=self.cohere_config.embedding_types,
                truncate=self.cohere_config.truncate
            )
            
            logging.info(f"Embedding job created: {embed_job_response.id}")
            return embed_job_response
            
        except Exception as e:
            logging.error(f"Failed to create embedding job: {e}")
            raise
    
    def wait_for_job_completion(self, embed_job: cohere.EmbedJob) -> cohere.EmbedJob:
        """
        Wait for an embedding job to complete.
        
        Args:
            embed_job: The embedding job to wait for
            
        Returns:
            Completed EmbedJob object
        """
        logging.info(f"Waiting for embedding job completion: {embed_job.id}")
        
        try:
            completed_job = self.cohere_client.wait(embed_job)
            logging.info(f"Embedding job completed: {completed_job.id}")
            return completed_job
            
        except Exception as e:
            logging.error(f"Embedding job failed: {e}")
            raise
    
    def download_embeddings(self, completed_job: cohere.EmbedJob, 
                          output_path: Optional[str] = None) -> str:
        """
        Download the embeddings from a completed job.
        
        Args:
            completed_job: Completed embedding job
            output_path: Optional path for output file. If None, creates temp file.
            
        Returns:
            Path to the downloaded embeddings file
        """
        logging.info(f"Downloading embeddings from job: {completed_job.id}")
        
        try:
            # Get the output dataset
            output_dataset_response = self.cohere_client.datasets.get(
                id=completed_job.output_dataset_id
            )
            output_dataset = output_dataset_response.dataset
            
            if output_path is None:
                # Create temporary file
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
                output_path = temp_file.name
                temp_file.close()
            
            # Save the dataset
            self.cohere_client.utils.save_dataset(
                dataset=output_dataset,
                filepath=output_path,
                format="csv"
            )
            
            logging.info(f"Embeddings downloaded to: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Failed to download embeddings: {e}")
            raise
    
    def process_chunks_to_embeddings(self, chunks_data: List[Dict[str, Any]], 
                                   dataset_name: str) -> str:
        """
        Complete workflow: process chunks through Cohere batch embedding API.
        
        Args:
            chunks_data: List of chunk dictionaries
            dataset_name: Name for the dataset
            
        Returns:
            Path to the downloaded embeddings file
        """
        logging.info(f"Starting Cohere batch embedding process for {len(chunks_data)} chunks")
        
        try:
            # Step 1: Create JSONL dataset
            jsonl_path = self.create_jsonl_dataset(chunks_data)
            
            # Step 2: Upload dataset
            dataset = self.upload_dataset(jsonl_path, dataset_name)
            
            # Step 3: Create embedding job
            embed_job = self.create_embedding_job(dataset.id)
            
            # Step 4: Wait for completion
            completed_job = self.wait_for_job_completion(embed_job)
            
            # Step 5: Download embeddings
            embeddings_path = self.download_embeddings(completed_job)
            
            # Cleanup temporary JSONL file
            try:
                os.unlink(jsonl_path)
            except Exception as e:
                logging.warning(f"Failed to cleanup temporary file {jsonl_path}: {e}")
            
            logging.info(f"Cohere batch embedding process completed. Results: {embeddings_path}")
            return embeddings_path
            
        except Exception as e:
            logging.error(f"Cohere batch embedding process failed: {e}")
            raise
    
    def parse_embeddings_csv(self, csv_path: str) -> List[Dict[str, Any]]:
        """
        Parse the embeddings CSV file and return structured data.
        
        Args:
            csv_path: Path to the embeddings CSV file
            
        Returns:
            List of dictionaries with chunk data and embeddings
        """
        import pandas as pd
        
        logging.info(f"Parsing embeddings CSV: {csv_path}")
        
        try:
            df = pd.read_csv(csv_path)
            results = []
            
            for _, row in df.iterrows():
                # Parse the embedding vector (assuming it's stored as a string representation)
                embedding_str = row.get('embeddings', '[]')
                try:
                    # Convert string representation to list of floats
                    embedding = json.loads(embedding_str) if isinstance(embedding_str, str) else embedding_str
                except:
                    logging.warning(f"Failed to parse embedding for row {row.name}")
                    embedding = []
                
                result = {
                    'text': row.get('text', ''),
                    'chunk_id': row.get('chunk_id', ''),
                    'metadata': row.get('metadata', {}),
                    'embedding': embedding
                }
                results.append(result)
            
            logging.info(f"Parsed {len(results)} embeddings from CSV")
            return results
            
        except Exception as e:
            logging.error(f"Failed to parse embeddings CSV: {e}")
            raise
