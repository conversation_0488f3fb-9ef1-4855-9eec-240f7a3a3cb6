#!/usr/bin/env python3
"""
Main script for PDF document analysis and chunking.

This script processes PDF files from the 'raw' folder, chunks them using the Chunker class,
and stores the chunks in JSON format in the 'staging' folder with the required structure.

Usage:
    python main.py                    # Process all PDFs in raw folder
    python main.py --pdf filename.pdf # Process specific PDF file
    python main.py --help            # Show help
"""

import argparse
import sys
from pathlib import Path
from kg_agents.pdf_processor import PDFProcessor
from kg_agents.config import config_manager


def main():
    """Main function to handle PDF processing."""
    parser = argparse.ArgumentParser(
        description="Process PDF files and create chunks for document analysis"
    )
    parser.add_argument(
        "--pdf",
        type=str,
        help="Specific PDF file to process (must be in raw folder)"
    )
    parser.add_argument(
        "--raw-folder",
        type=str,
        default="data_processing_steps/raw",
        help="Path to raw PDF folder (default: data_processing_steps/raw)"
    )
    parser.add_argument(
        "--staging-folder",
        type=str,
        default="data_processing_steps/staging",
        help="Path to staging folder for chunks (default: data_processing_steps/staging)"
    )
    parser.add_argument(
        "--max-chunks",
        type=int,
        default=45,
        help="Maximum number of chunks per PDF (default: 45)"
    )
    parser.add_argument(
        "--min-tokens",
        type=int,
        default=500,
        help="Minimum tokens per chunk (default: 500)"
    )
    parser.add_argument(
        "--list-pdfs",
        action="store_true",
        help="List all PDF files in the raw folder"
    )
    
    args = parser.parse_args()
    
    # Update configuration if provided
    if args.max_chunks != 45:
        config_manager.set_max_chunks(args.max_chunks)
    if args.min_tokens != 500:
        config_manager.set_min_tokens_per_chunk(args.min_tokens)
    
    # Initialize processor
    processor = PDFProcessor(args.raw_folder, args.staging_folder)
    
    # List PDFs if requested
    if args.list_pdfs:
        pdf_files = [f for f in Path(args.raw_folder).iterdir() if f.suffix.lower() == '.pdf']
        if pdf_files:
            print("PDF files in raw folder:")
            for pdf_file in pdf_files:
                print(f"  - {pdf_file.name}")
        else:
            print("No PDF files found in raw folder.")
        return
    
    try:
        if args.pdf:
            # Process specific PDF
            print(f"Processing specific PDF: {args.pdf}")
            result = processor.process_pdf(args.pdf)
            print_result(result)
        else:
            # Process all PDFs
            print("Processing all PDFs in raw folder...")
            results = processor.process_all_pdfs()
            
            if not results:
                print("No PDF files found to process.")
                return
            
            print(f"\nProcessing Summary:")
            print(f"{'='*50}")
            
            successful = 0
            failed = 0
            total_chunks = 0
            
            for result in results:
                print_result(result)
                if result["status"] == "success":
                    successful += 1
                    total_chunks += result["chunks_created"]
                else:
                    failed += 1
            
            print(f"\nOverall Results:")
            print(f"  Successfully processed: {successful} PDFs")
            print(f"  Failed: {failed} PDFs")
            print(f"  Total chunks created: {total_chunks}")
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def print_result(result):
    """Print processing result in a formatted way."""
    print(f"\nPDF: {result['pdf_name']}")
    print(f"  Status: {result['status']}")
    
    if result['status'] == 'success':
        print(f"  Chunks created: {result['chunks_created']}")
        print(f"  Folder: {result['folder_path']}")
    elif result['status'] == 'error':
        print(f"  Error: {result.get('error', 'Unknown error')}")
    elif result['status'] == 'no_text':
        print(f"  Warning: No text could be extracted from this PDF")


if __name__ == "__main__":
    main()
