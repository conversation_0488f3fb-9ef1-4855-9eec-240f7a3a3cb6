"""Data processing workflow for scanning staging folders and processing JSON chunks."""

import os
import json
import logging
from typing import List, Dict, Any, <PERSON><PERSON>
from pathlib import Path
from neo4j_ingestion.config import ConfigManager
from neo4j_ingestion.cohere_embedding_service import CohereEmbeddingService


class DataProcessor:
    """Handles scanning staging folders and processing JSON chunk files."""
    
    def __init__(self, config_manager: ConfigManager, staging_path: str = "data_processing_steps/staging"):
        self.config_manager = config_manager
        self.staging_path = Path(staging_path)
        self.cohere_service = CohereEmbeddingService(config_manager)
        
    def scan_staging_folders(self) -> List[Tuple[int, str, Path]]:
        """
        Scan the staging directory and return indexed folders.
        
        Returns:
            List of tuples: (index, folder_name, folder_path)
        """
        logging.info(f"Scanning staging directory: {self.staging_path}")
        
        if not self.staging_path.exists():
            logging.warning(f"Staging directory does not exist: {self.staging_path}")
            return []
        
        folders = []
        for item in self.staging_path.iterdir():
            if item.is_dir():
                folders.append(item)
        
        # Sort folders for consistent indexing
        folders.sort(key=lambda x: x.name)
        
        # Create indexed list
        indexed_folders = [(i, folder.name, folder) for i, folder in enumerate(folders)]
        
        logging.info(f"Found {len(indexed_folders)} folders in staging directory")
        for index, name, path in indexed_folders:
            logging.info(f"  [{index}] {name}")
        
        return indexed_folders
    
    def read_json_files_from_folder(self, folder_path: Path) -> List[Dict[str, Any]]:
        """
        Read all JSON files from a folder and extract their content.
        
        Args:
            folder_path: Path to the folder containing JSON files
            
        Returns:
            List of dictionaries containing JSON file contents
        """
        logging.info(f"Reading JSON files from: {folder_path}")
        
        json_files = list(folder_path.glob("*.json"))
        if not json_files:
            logging.warning(f"No JSON files found in {folder_path}")
            return []
        
        chunks_data = []
        for json_file in sorted(json_files):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Add file information to the data
                data['source_file'] = json_file.name
                data['source_folder'] = folder_path.name
                
                # Ensure chunk_id is set
                if 'chunk_id' not in data:
                    data['chunk_id'] = json_file.stem  # Use filename without extension
                
                chunks_data.append(data)
                logging.debug(f"Loaded {json_file.name}")
                
            except Exception as e:
                logging.error(f"Failed to read {json_file}: {e}")
                continue
        
        logging.info(f"Successfully loaded {len(chunks_data)} JSON files from {folder_path}")
        return chunks_data
    
    def extract_text_fields(self, chunks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract and validate text fields from chunk data.
        
        Args:
            chunks_data: List of chunk dictionaries
            
        Returns:
            List of chunks with validated text fields
        """
        logging.info(f"Extracting text fields from {len(chunks_data)} chunks")
        
        valid_chunks = []
        for i, chunk in enumerate(chunks_data):
            text = chunk.get('text', '')
            if not text or not isinstance(text, str):
                logging.warning(f"Chunk {i} has invalid or missing text field")
                continue
            
            # Clean and validate text
            text = text.strip()
            if len(text) < 10:  # Skip very short texts
                logging.warning(f"Chunk {i} has text too short ({len(text)} chars)")
                continue
            
            valid_chunks.append(chunk)
        
        logging.info(f"Extracted {len(valid_chunks)} valid text chunks")
        return valid_chunks
    
    def process_folder_with_cohere(self, folder_index: int, folder_name: str, 
                                 folder_path: Path) -> List[Dict[str, Any]]:
        """
        Process a single folder through the complete Cohere embedding workflow.
        
        Args:
            folder_index: Index of the folder
            folder_name: Name of the folder
            folder_path: Path to the folder
            
        Returns:
            List of chunks with embeddings
        """
        logging.info(f"Processing folder [{folder_index}] {folder_name} with Cohere")
        
        try:
            # Step 1: Read JSON files
            chunks_data = self.read_json_files_from_folder(folder_path)
            if not chunks_data:
                logging.warning(f"No data found in folder {folder_name}")
                return []
            
            # Step 2: Extract and validate text fields
            valid_chunks = self.extract_text_fields(chunks_data)
            if not valid_chunks:
                logging.warning(f"No valid text chunks found in folder {folder_name}")
                return []
            
            # Step 3: Process through Cohere batch embedding API
            dataset_name = f"gd302_folder_{folder_index}_{folder_name}"
            embeddings_csv_path = self.cohere_service.process_chunks_to_embeddings(
                valid_chunks, dataset_name
            )
            
            # Step 4: Parse embeddings and combine with original data
            embedded_chunks = self.cohere_service.parse_embeddings_csv(embeddings_csv_path)
            
            # Step 5: Merge embeddings with original chunk data
            final_chunks = self.merge_embeddings_with_chunks(valid_chunks, embedded_chunks)
            
            # Cleanup temporary embeddings file
            try:
                os.unlink(embeddings_csv_path)
            except Exception as e:
                logging.warning(f"Failed to cleanup embeddings file {embeddings_csv_path}: {e}")
            
            logging.info(f"Successfully processed folder {folder_name} with {len(final_chunks)} embedded chunks")
            return final_chunks
            
        except Exception as e:
            logging.error(f"Failed to process folder {folder_name}: {e}")
            raise
    
    def merge_embeddings_with_chunks(self, original_chunks: List[Dict[str, Any]], 
                                   embedded_chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge embedding data with original chunk data.
        
        Args:
            original_chunks: Original chunk data
            embedded_chunks: Chunks with embeddings from Cohere
            
        Returns:
            Merged chunk data with embeddings
        """
        logging.info("Merging embeddings with original chunk data")
        
        # Create a mapping of chunk_id to embedding
        embedding_map = {}
        for embedded_chunk in embedded_chunks:
            chunk_id = embedded_chunk.get('chunk_id', '')
            embedding = embedded_chunk.get('embedding', [])
            if chunk_id and embedding:
                embedding_map[chunk_id] = embedding
        
        # Merge embeddings with original data
        merged_chunks = []
        for chunk in original_chunks:
            chunk_id = chunk.get('chunk_id', '')
            embedding = embedding_map.get(chunk_id, [])
            
            if embedding:
                chunk['embedding'] = embedding
                merged_chunks.append(chunk)
            else:
                logging.warning(f"No embedding found for chunk {chunk_id}")
        
        logging.info(f"Merged {len(merged_chunks)} chunks with embeddings")
        return merged_chunks
    
    def process_all_staging_folders(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Process all folders in the staging directory.
        
        Returns:
            Dictionary mapping folder names to processed chunks
        """
        logging.info("Starting processing of all staging folders")
        
        # Scan for folders
        indexed_folders = self.scan_staging_folders()
        if not indexed_folders:
            logging.warning("No folders found in staging directory")
            return {}
        
        results = {}
        for folder_index, folder_name, folder_path in indexed_folders:
            try:
                processed_chunks = self.process_folder_with_cohere(
                    folder_index, folder_name, folder_path
                )
                results[folder_name] = processed_chunks
                
            except Exception as e:
                logging.error(f"Failed to process folder {folder_name}: {e}")
                results[folder_name] = []
        
        total_chunks = sum(len(chunks) for chunks in results.values())
        logging.info(f"Completed processing all folders. Total chunks: {total_chunks}")
        
        return results
    
    def save_processed_results(self, results: Dict[str, List[Dict[str, Any]]], 
                             output_path: str = "neo4j_ingestion/cohere_processed_chunks.json"):
        """
        Save processed results to a JSON file.
        
        Args:
            results: Dictionary of processed chunks by folder
            output_path: Path to save the results
        """
        logging.info(f"Saving processed results to: {output_path}")
        
        try:
            # Flatten results for easier processing
            all_chunks = []
            for folder_name, chunks in results.items():
                for chunk in chunks:
                    chunk['source_folder'] = folder_name
                    all_chunks.append(chunk)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(all_chunks, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Saved {len(all_chunks)} processed chunks to {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to save processed results: {e}")
            raise
