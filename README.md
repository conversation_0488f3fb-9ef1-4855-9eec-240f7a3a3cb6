# GENAI_GD302_Agentic_document_analysis

An intelligent document analysis system that processes PDF files, chunks them into manageable pieces, and stores them in a structured format for further analysis.

## Project Structure

```
├── data_processing_steps/  # Consolidated data processing workflows
│   ├── raw/               # Store raw PDF files here
│   ├── staging/           # Processed chunks are stored here
│   │   └── chunks_n_name_files/  # Auto-generated folders for each PDF
│   │       ├── chunk_0.j<PERSON>
│   │       ├── chunk_1.json
│   │       └── ...
│   ├── visuals/           # Extracted images from PDFs
│   └── raw_md/            # Consolidated markdown files
├── kg_agents/             # Core processing modules
│   ├── __init__.py
│   ├── config.py          # Configuration management
│   ├── chunker.py         # Text chunking logic
│   └── pdf_processor.py   # PDF processing and storage
├── main.py               # Main script for processing
└── requirements.txt      # Python dependencies
```

## Features

- **PDF Text Extraction**: Extracts text from PDF files using PyPDF2
- **Intelligent Chunking**: Sentence-aware chunking with configurable token limits
- **Structured Storage**: Organizes chunks in folders with naming convention `chunks_n_name_files`
- **JSON Format**: Each chunk stored as `chunk_n.json` with metadata
- **Configurable**: Adjustable chunk size, token limits, and processing parameters
- **Batch Processing**: Process single PDFs or entire folders

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Download NLTK data (required for sentence tokenization):
```python
import nltk
nltk.download('punkt')
```

## Usage

### Basic Usage

1. **Add PDF files** to the `raw/` folder
2. **Run the processor**:
```bash
python main.py
```

This will process all PDF files in the `raw/` folder and create chunks in the `staging/` folder.

### Advanced Usage

**Process a specific PDF:**
```bash
python main.py --pdf document.pdf
```

**Configure chunking parameters:**
```bash
python main.py --max-chunks 30 --min-tokens 300
```

**List available PDFs:**
```bash
python main.py --list-pdfs
```

**Use custom folders:**
```bash
python main.py --raw-folder /path/to/pdfs --staging-folder /path/to/output
```

### Command Line Options

- `--pdf FILENAME`: Process specific PDF file
- `--max-chunks N`: Maximum chunks per PDF (default: 45)
- `--min-tokens N`: Minimum tokens per chunk (default: 500)
- `--raw-folder PATH`: Custom raw folder path (default: raw)
- `--staging-folder PATH`: Custom staging folder path (default: staging)
- `--list-pdfs`: List all PDF files in raw folder
- `--help`: Show help message

## Output Format

### Folder Structure
For each processed PDF, a folder is created with the format:
```
chunks_n_name_files/
```
Where:
- `n` is a unique integer
- `name` is the PDF filename (without extension)

### Chunk Files
Each chunk is saved as `chunk_n.json` with the following structure:
```json
{
  "chunk_index": 0,
  "chunk_id": "chunk_0",
  "text": "The actual text content of the chunk...",
  "token_count": 542,
  "created_at": "uuid-string"
}
```

## Configuration

The system can be configured through:

1. **Command line arguments** (temporary)
2. **Environment variables** (persistent):
   - `TOKENIZER_NAME`: Tokenizer to use (default: cl100k_base)
   - `MAX_CHUNKS`: Maximum chunks per PDF (default: 45)
   - `MIN_TOKENS_PER_CHUNK`: Minimum tokens per chunk (default: 500)

## Example Workflow

1. Place PDF files in `data_processing_steps/raw/` folder:
```
data_processing_steps/raw/
├── research_paper.pdf
└── manual.pdf
```

2. Run the processor:
```bash
python main.py
```

3. Check the results in `data_processing_steps/staging/`:
```
data_processing_steps/staging/
├── chunks_1_research_paper_files/
│   ├── chunk_0.json
│   ├── chunk_1.json
│   └── chunk_2.json
└── chunks_2_manual_files/
    ├── chunk_0.json
    ├── chunk_1.json
    ├── chunk_2.json
    └── chunk_3.json
```

## Dependencies

- **PyPDF2**: PDF text extraction
- **tiktoken**: Token counting and encoding
- **nltk**: Natural language processing (sentence tokenization)

## Error Handling

The system handles various error conditions:
- Missing PDF files
- Corrupted or unreadable PDFs
- PDFs with no extractable text
- File system permissions issues

All errors are logged with descriptive messages to help with troubleshooting.
GD302 project for conducting analysis on a set of documents to extract insights.
