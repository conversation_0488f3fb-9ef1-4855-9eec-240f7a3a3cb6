#!/usr/bin/env python3
"""
Enhanced workflow script for converting JSON chunks to markdown files.

This script implements the enhanced workflow:
1. Read the directory of chunks (staging folder) and assign each folder an index
2. For every index assigned, access the N index starting from 0
3. For every JSON file in the folder, extract the "text" key and its content
4. In the raw_md folder, create a single md file as md_original_folder_name_n
5. Loop until all indices are processed

Usage:
    python enhanced_workflow.py                    # Process all chunk folders
    python enhanced_workflow.py --index 0         # Process specific folder by index
    python enhanced_workflow.py --list-folders    # List all available chunk folders
    python enhanced_workflow.py --help            # Show help
"""

import argparse
import sys
from pathlib import Path
from kg_agents.md_converter import MarkdownConverter


def main():
    """Main function to handle the enhanced workflow."""
    parser = argparse.ArgumentParser(
        description="Enhanced workflow: Convert JSON chunks to markdown files"
    )
    parser.add_argument(
        "--index",
        type=int,
        help="Specific folder index to process (starting from 0)"
    )
    parser.add_argument(
        "--staging-folder",
        type=str,
        default="data_processing_steps/staging",
        help="Path to staging folder containing chunks (default: data_processing_steps/staging)"
    )
    parser.add_argument(
        "--raw-md-folder",
        type=str,
        default="data_processing_steps/raw_md",
        help="Path to raw_md folder for markdown files (default: data_processing_steps/raw_md)"
    )
    parser.add_argument(
        "--list-folders",
        action="store_true",
        help="List all available chunk folders with their indices"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize converter
        converter = MarkdownConverter(args.staging_folder, args.raw_md_folder)
        
        # List folders if requested
        if args.list_folders:
            chunk_folders = converter.get_chunk_folders()
            if chunk_folders:
                print("Available chunk folders:")
                print(f"{'Index':<6} {'Folder Name'}")
                print("-" * 50)
                for i, folder in enumerate(chunk_folders):
                    print(f"{i:<6} {folder.name}")
                print(f"\nTotal folders: {len(chunk_folders)}")
            else:
                print("No chunk folders found in staging directory.")
            return
        
        if args.index is not None:
            # Process specific folder by index
            print(f"Processing folder at index: {args.index}")
            result_path = converter.process_specific_folder(args.index)
            
            if result_path:
                print(f"\nSuccess! Created markdown file: {result_path}")
            else:
                print(f"\nFailed to process folder at index {args.index}")
                sys.exit(1)
        else:
            # Process all folders
            print("Starting enhanced workflow: Converting all JSON chunks to markdown files...")
            print("=" * 70)
            
            result = converter.process_all_folders()
            
            if result["status"] == "no_folders":
                print("No chunk folders found to process.")
                print("\nTo create chunks first, run:")
                print("  python main.py")
                return
            elif result["status"] == "success":
                print("\nEnhanced workflow completed successfully!")
                print(f"Processed {result['processed_folders']} out of {result['total_folders']} folders")
                
                if result["created_files"]:
                    print("\nCreated markdown files:")
                    for file_path in result["created_files"]:
                        print(f"  - {Path(file_path).name}")
                
                print(f"\nAll markdown files are saved in: {args.raw_md_folder}/")
            else:
                print("Enhanced workflow completed with some issues.")
                sys.exit(1)
            
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("\nMake sure you have run the main chunking process first:")
        print("  python main.py")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


def print_workflow_info():
    """Print information about the enhanced workflow."""
    print("Enhanced Workflow Steps:")
    print("1. Read the staging directory and assign each chunk folder an index")
    print("2. For each index (starting from 0), access the corresponding folder")
    print("3. Extract 'text' content from all JSON files in the folder")
    print("4. Create a markdown file: md_original_folder_name_n.md")
    print("5. Repeat until all folders are processed")
    print()


if __name__ == "__main__":
    print("Enhanced Workflow - JSON to Markdown Converter")
    print("=" * 50)
    print_workflow_info()
    main()
