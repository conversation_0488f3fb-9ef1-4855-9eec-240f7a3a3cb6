#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sample chunk data for testing the enhanced workflow.
This creates sample JSON chunk files in the staging folder structure.
"""

import json
import os
from pathlib import Path


def create_sample_chunks():
    """Create sample chunk folders and JSON files for testing."""
    
    # Create staging folder if it doesn't exist
    staging_folder = Path("staging")
    staging_folder.mkdir(exist_ok=True)
    
    # Sample data for different "PDFs"
    sample_data = [
        {
            "folder_name": "chunks_1_sample_document_files",
            "chunks": [
                {
                    "chunk_index": 0,
                    "chunk_id": "chunk_001",
                    "text": "This is the first chunk of the sample document. It contains introductory information about the topic being discussed. The content here would typically be extracted from a PDF file and processed through the chunking system.",
                    "token_count": 45,
                    "created_at": "sample_uuid_001"
                },
                {
                    "chunk_index": 1,
                    "chunk_id": "chunk_002", 
                    "text": "This is the second chunk containing more detailed information. It builds upon the concepts introduced in the first chunk and provides additional context and examples that help explain the subject matter in greater depth.",
                    "token_count": 42,
                    "created_at": "sample_uuid_002"
                },
                {
                    "chunk_index": 2,
                    "chunk_id": "chunk_003",
                    "text": "The third chunk concludes the sample document with final thoughts and summary points. It ties together the information from previous chunks and provides actionable insights for the reader.",
                    "token_count": 38,
                    "created_at": "sample_uuid_003"
                }
            ]
        },
        {
            "folder_name": "chunks_2_research_paper_files",
            "chunks": [
                {
                    "chunk_index": 0,
                    "chunk_id": "chunk_004",
                    "text": "Abstract: This research paper investigates the applications of artificial intelligence in document processing. The study examines various methodologies for extracting and analyzing textual content from PDF documents.",
                    "token_count": 35,
                    "created_at": "sample_uuid_004"
                },
                {
                    "chunk_index": 1,
                    "chunk_id": "chunk_005",
                    "text": "Introduction: Document analysis has become increasingly important in the digital age. With the proliferation of digital documents, automated systems for processing and understanding textual content are essential for efficient information management.",
                    "token_count": 40,
                    "created_at": "sample_uuid_005"
                }
            ]
        },
        {
            "folder_name": "chunks_3_technical_manual_files",
            "chunks": [
                {
                    "chunk_index": 0,
                    "chunk_id": "chunk_006",
                    "text": "Chapter 1: Getting Started. This technical manual provides comprehensive instructions for setting up and configuring the document analysis system. Follow these steps carefully to ensure proper installation.",
                    "token_count": 35,
                    "created_at": "sample_uuid_006"
                },
                {
                    "chunk_index": 1,
                    "chunk_id": "chunk_007",
                    "text": "Chapter 2: Configuration. The system requires specific configuration parameters to function optimally. This section details each parameter and its recommended values for different use cases.",
                    "token_count": 32,
                    "created_at": "sample_uuid_007"
                },
                {
                    "chunk_index": 2,
                    "chunk_id": "chunk_008",
                    "text": "Chapter 3: Troubleshooting. Common issues and their solutions are documented in this section. If you encounter problems during operation, consult this troubleshooting guide first.",
                    "token_count": 30,
                    "created_at": "sample_uuid_008"
                },
                {
                    "chunk_index": 3,
                    "chunk_id": "chunk_009",
                    "text": "Chapter 4: Advanced Features. This section covers advanced functionality including custom processing pipelines, integration with external systems, and performance optimization techniques.",
                    "token_count": 28,
                    "created_at": "sample_uuid_009"
                }
            ]
        }
    ]
    
    created_folders = []
    total_chunks = 0
    
    # Create folders and JSON files
    for data in sample_data:
        folder_path = staging_folder / data["folder_name"]
        folder_path.mkdir(exist_ok=True)
        created_folders.append(folder_path)
        
        print(f"Creating folder: {data['folder_name']}")
        
        # Create JSON files for each chunk
        for chunk in data["chunks"]:
            chunk_filename = f"chunk_{chunk['chunk_index']}.json"
            chunk_file_path = folder_path / chunk_filename
            
            with open(chunk_file_path, 'w', encoding='utf-8') as f:
                json.dump(chunk, f, indent=2, ensure_ascii=False)
            
            print(f"  Created: {chunk_filename}")
            total_chunks += 1
    
    print(f"\nSample data creation complete!")
    print(f"Created {len(created_folders)} folders with {total_chunks} total chunks")
    print(f"Folders created in: {staging_folder.absolute()}")
    
    return created_folders


if __name__ == "__main__":
    print("Creating sample chunk data for testing...")
    print("=" * 50)
    create_sample_chunks()
    print("\nYou can now test the enhanced workflow with:")
    print("  python enhanced_workflow.py --list-folders")
    print("  python enhanced_workflow.py")
