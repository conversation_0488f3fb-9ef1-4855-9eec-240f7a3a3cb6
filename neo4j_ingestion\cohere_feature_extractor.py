"""Enhanced feature extraction module with Cohere embedding support."""

import os
import json
import re
import logging
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from neo4j_ingestion.config import ConfigManager
from neo4j_ingestion.data_processor import DataProcessor


class CohereFeatureExtractor:
    """Enhanced feature extractor that uses Cohere for embeddings and OpenAI for key phrases."""
    
    def __init__(self, config_manager: ConfigManager, use_cohere_embeddings: bool = True):
        self.config_manager = config_manager
        self.use_cohere_embeddings = use_cohere_embeddings
        
        # Initialize OpenAI client for key phrases (still using OpenAI for this)
        self.openai_client = config_manager.get_openai_client()
        self.chat_model = config_manager.openai_config.chat_model
        
        # Initialize Cohere client if using Cohere embeddings
        if use_cohere_embeddings:
            try:
                self.cohere_client = config_manager.get_cohere_client()
                self.cohere_config = config_manager.cohere_config
                logging.info("Initialized Cohere client for embeddings")
            except Exception as e:
                logging.warning(f"Failed to initialize Cohere client: {e}")
                logging.info("Falling back to OpenAI embeddings")
                self.use_cohere_embeddings = False
                self.embedding_model = config_manager.openai_config.embedding_model
        else:
            self.embedding_model = config_manager.openai_config.embedding_model
    
    def get_embedding_openai(self, text: str) -> List[float]:
        """Generate embedding using OpenAI."""
        try:
            text = text.replace("\n", " ")
            response = self.openai_client.embeddings.create(
                input=[text], 
                model=self.embedding_model
            )
            return response.data[0].embedding
        except Exception as e:
            logging.error(f"Error generating OpenAI embedding: {e}")
            return []
    
    def get_embedding_cohere_single(self, text: str) -> List[float]:
        """Generate single embedding using Cohere (for individual chunks)."""
        try:
            text = text.replace("\n", " ")
            response = self.cohere_client.embed(
                texts=[text],
                model=self.cohere_config.embedding_model,
                input_type=self.cohere_config.input_type,
                embedding_types=self.cohere_config.embedding_types,
                truncate=self.cohere_config.truncate
            )
            
            # Extract the embedding from the response
            if response.embeddings and response.embeddings.float_:
                return response.embeddings.float_[0]
            else:
                logging.warning("No float embeddings returned from Cohere")
                return []
                
        except Exception as e:
            logging.error(f"Error generating Cohere embedding: {e}")
            return []
    
    def get_embedding(self, text: str) -> List[float]:
        """Generate embedding using the configured provider."""
        if self.use_cohere_embeddings:
            return self.get_embedding_cohere_single(text)
        else:
            return self.get_embedding_openai(text)
    
    def extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases using OpenAI (keeping this as is)."""
        prompt = f"""
        From the text provided below, extract the top 5-7 most important key phrases, concepts, or named entities.
        Return the result as a JSON object with a "phrases" key containing a list of strings.
        Example: {{"phrases": ["Machine Learning", "Large Language Models", "Data Science"]}}
        
        Text:
        ---
        {text}
        ---
        """
        
        try:
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
                response_format={"type": "json_object"}
            )
            
            result_json = json.loads(response.choices[0].message.content)
            return result_json.get("phrases", [])
            
        except (json.JSONDecodeError, KeyError) as e:
            logging.error(f"Error parsing phrases from OpenAI response: {e}")
            return []
        except Exception as e:
            logging.error(f"Error extracting key phrases: {e}")
            return []
    
    def tokenize_text(self, text: str) -> Dict[str, int]:
        """Simple tokenizer to get words and their frequencies."""
        tokens = re.findall(r'\b\w+\b', text.lower())
        frequency = {}
        for token in tokens:
            frequency[token] = frequency.get(token, 0) + 1
        return frequency
    
    def process_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Dict[str, Any]:
        """Process a single chunk to extract all features."""
        text = chunk_data.get('text', '')
        if not text:
            logging.warning(f"No text found in chunk {chunk_id}")
            return {}
        
        logging.info(f"Processing chunk {chunk_id}")
        
        try:
            # Extract all features
            embedding = self.get_embedding(text)
            key_phrases = self.extract_key_phrases(text)
            tokens_freq = self.tokenize_text(text)
            
            result = {
                'chunk_id': chunk_id,
                'text': text,
                'embedding': embedding,
                'key_phrases': key_phrases,
                'tokens_frequency': tokens_freq,
                'metadata': chunk_data.get('metadata', {}),
                'embedding_provider': 'cohere' if self.use_cohere_embeddings else 'openai'
            }
            
            logging.info(f"Successfully processed chunk {chunk_id}")
            return result
            
        except Exception as e:
            logging.error(f"Error processing chunk {chunk_id}: {e}")
            return {}


class CohereStagingProcessor:
    """Enhanced staging processor that can use Cohere batch embeddings or individual processing."""
    
    def __init__(self, staging_path: str = "data_processing_steps/staging", 
                 use_batch_embeddings: bool = True):
        self.staging_path = Path(staging_path)
        self.use_batch_embeddings = use_batch_embeddings
        self.config_manager = None
        self.feature_extractor = None
        self.data_processor = None
        
    def initialize(self, config_manager: ConfigManager):
        """Initialize the processor with configuration."""
        self.config_manager = config_manager
        
        if self.use_batch_embeddings:
            # Use DataProcessor for batch Cohere embeddings
            self.data_processor = DataProcessor(config_manager, str(self.staging_path))
            logging.info("Initialized for Cohere batch embedding processing")
        else:
            # Use individual embedding processing
            self.feature_extractor = CohereFeatureExtractor(config_manager, use_cohere_embeddings=True)
            logging.info("Initialized for individual Cohere embedding processing")
    
    def process_with_batch_embeddings(self) -> List[Dict[str, Any]]:
        """Process all staging folders using Cohere batch embeddings."""
        if not self.data_processor:
            raise ValueError("Data processor not initialized for batch processing")
        
        logging.info("Starting batch embedding processing with Cohere")
        
        # Process all folders
        results = self.data_processor.process_all_staging_folders()
        
        # Flatten results
        all_chunks = []
        for folder_name, chunks in results.items():
            for chunk in chunks:
                chunk['folder_name'] = folder_name
                chunk['embedding_provider'] = 'cohere_batch'
                all_chunks.append(chunk)
        
        logging.info(f"Batch processing completed. Total chunks: {len(all_chunks)}")
        return all_chunks
    
    def process_with_individual_embeddings(self, max_workers: int = 4) -> List[Dict[str, Any]]:
        """Process staging folders using individual Cohere embeddings."""
        if not self.feature_extractor:
            raise ValueError("Feature extractor not initialized for individual processing")
        
        logging.info("Starting individual embedding processing with Cohere")
        
        all_processed_chunks = []
        folders = self.discover_staging_folders()
        
        if not folders:
            logging.warning("No staging folders found")
            return all_processed_chunks
        
        for folder_idx, folder_path in folders:
            logging.info(f"Processing folder {folder_idx}: {folder_path.name}")
            chunks = self.load_chunk_files(folder_path)
            
            if not chunks:
                logging.warning(f"No chunks found in folder {folder_path.name}")
                continue
            
            # Process chunks in parallel
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_chunk = {
                    executor.submit(self.feature_extractor.process_chunk, chunk_data, chunk_id): chunk_id
                    for chunk_id, chunk_data in chunks
                }
                
                for future in as_completed(future_to_chunk):
                    chunk_id = future_to_chunk[future]
                    try:
                        result = future.result()
                        if result:
                            result['folder_index'] = folder_idx
                            result['folder_name'] = folder_path.name
                            all_processed_chunks.append(result)
                    except Exception as e:
                        logging.error(f"Error processing chunk {chunk_id}: {e}")
        
        logging.info(f"Individual processing completed. Total chunks: {len(all_processed_chunks)}")
        return all_processed_chunks
    
    def discover_staging_folders(self) -> List[Tuple[int, Path]]:
        """Discover and index staging folders."""
        folders = []
        if not self.staging_path.exists():
            logging.warning(f"Staging path {self.staging_path} does not exist")
            return folders
        
        for idx, folder in enumerate(self.staging_path.iterdir()):
            if folder.is_dir():
                folders.append((idx, folder))
                logging.info(f"Found staging folder {idx}: {folder.name}")
        
        return sorted(folders, key=lambda x: x[1].name)
    
    def load_chunk_files(self, folder_path: Path) -> List[Tuple[str, Dict[str, Any]]]:
        """Load all chunk JSON files from a folder."""
        chunks = []
        
        for chunk_file in folder_path.glob('*.json'):
            try:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunk_data = json.load(f)
                    chunk_id = f"{folder_path.name}_{chunk_file.stem}"
                    chunks.append((chunk_id, chunk_data))
            except Exception as e:
                logging.error(f"Error loading chunk file {chunk_file}: {e}")
        
        return chunks
    
    def process_staging_folders(self, max_workers: int = 4) -> List[Dict[str, Any]]:
        """Process staging folders using the configured method."""
        if self.use_batch_embeddings:
            return self.process_with_batch_embeddings()
        else:
            return self.process_with_individual_embeddings(max_workers)
    
    def save_processed_chunks(self, processed_chunks: List[Dict[str, Any]], 
                            output_file: str = "neo4j_ingestion/cohere_processed_chunks.json"):
        """Save processed chunks to a JSON file."""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(processed_chunks, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Saved {len(processed_chunks)} processed chunks to {output_file}")
        except Exception as e:
            logging.error(f"Error saving processed chunks: {e}")
            raise
