# Visual PDF Processing System

This document describes the parallel visual PDF processing system that extracts and analyzes both text and images from PDF documents.

## Overview

The visual processing system runs in parallel to the main PDF processing pipeline and provides enhanced functionality for documents containing visual content like charts, graphs, diagrams, and images.

## Features

- **Text and Image Extraction**: Uses PyMuPDF to extract text blocks and images with precise coordinate information
- **Text-Image Association**: Automatically finds relationships between text blocks and nearby images
- **Visual Chunking**: Groups related text blocks and associates them with relevant images
- **Vision Model Integration**: Uses OpenAI's GPT-4o-mini vision model to analyze and describe images
- **Structured Output**: Generates JSON chunks with visual citations and descriptions

## Folder Structure

```
data_processing_steps/
├── raw/                    # Place PDF files here for visual processing
├── staging/                # Processed visual chunks stored here
│   └── chunks_n_name_files/
│       ├── chunk_0.json
│       ├── chunk_1.json
│       └── ...
├── visuals/               # Extracted images saved here
│   ├── document_page_0_img_0.png
│   ├── document_page_1_img_0.png
│   └── ...
└── raw_md/                # Consolidated markdown files
```

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up OpenAI API key (for vision model) - choose one method:

**Method 1: JSON Config File (Recommended)**
```bash
# Copy the template
cp kg_agents/openai_config_template.json kg_agents/openai_config.json

# Edit kg_agents/openai_config.json and add your API key:
{
  "api_key": "sk-your-actual-api-key-here",
  "model": "gpt-4o-mini",
  "description": "OpenAI API configuration for vision analysis"
}
```

**Method 2: Environment Variable**
```bash
export OPENAI_API_KEY="your-api-key-here"
```

**Method 3: Programmatically**
```python
from kg_agents.vision_analyzer import VisionAnalyzer
VisionAnalyzer.create_config_file('your-api-key-here')
```

## Usage

### Command Line Interface

```bash
# Setup folder structure
python visual_main.py --setup-folders

# Process all PDFs with vision analysis
python visual_main.py

# Process specific PDF
python visual_main.py --pdf document.pdf

# Process without vision model (faster, no image analysis)
python visual_main.py --no-vision

# List available PDFs
python visual_main.py --list-pdfs
```

### Programmatic Usage

```python
from kg_agents.visual_pdf_processor import VisualPDFProcessor
from kg_agents.vision_analyzer import VisionAnalyzer

# Initialize components
processor = VisualPDFProcessor()

# Initialize vision analyzer (will automatically load from config file or environment)
vision_analyzer = VisionAnalyzer()

# Or specify a custom config file
vision_analyzer = VisionAnalyzer(config_file="path/to/your/config.json")

# Or provide API key directly
vision_analyzer = VisionAnalyzer(api_key="your-api-key-here")

# Process a PDF with visual analysis
result = processor.process_pdf_with_visuals("document.pdf", vision_analyzer)

# Process without vision analysis
result = processor.process_pdf_with_visuals("document.pdf", vision_analyzer=None)
```

## Output Format

Each visual chunk is saved as a JSON file with the following structure:

```json
{
  "chunk_id": "visual_chunk_0",
  "source_document": "document.pdf",
  "page_number": 5,
  "content_type": "text_with_visuals",
  "text": "The analysis shows a significant upward trend in Q3, as illustrated in the chart below. [see: visual_ref_1] This trend is projected to continue into the next fiscal year.",
  "visual_citations": [
    {
      "ref_id": "visual_ref_1",
      "source_image_path": "./visuals/document_page_5_img_0.png",
      "description_from_vision_model": "A bar chart titled 'Quarterly Revenue Growth' showing revenue increasing from $1.2M in Q1 to $3.5M in Q3. The x-axis represents quarters and the y-axis represents revenue in millions.",
      "image_bbox": [100.0, 200.0, 400.0, 350.0]
    }
  ]
}
```

## Key Components

### VisualPDFProcessor
- Main processing class for visual PDF analysis
- Handles text and image extraction using PyMuPDF
- Creates visual chunks with text-image associations
- Manages folder structure and file organization

### VisionAnalyzer
- Integrates with OpenAI's vision model
- Analyzes extracted images and provides descriptions
- Handles batch processing of multiple images
- Provides error handling and retry logic

### Text-Image Association Algorithm
1. Extract text blocks and images with bounding box coordinates
2. Calculate distances between image centers and text block centers
3. Find closest text blocks to each image
4. Group related text blocks based on proximity
5. Associate images with their closest text groups

## Configuration

The system uses the same configuration as the main processing pipeline but operates independently. Key settings:

- **Raw Folder**: `data_processing_steps/raw` (default)
- **Staging Folder**: `data_processing_steps/staging` (default)
- **Visuals Folder**: `data_processing_steps/visuals` (default)
- **Vision Model**: `gpt-4o-mini` (OpenAI)
- **Proximity Threshold**: 50.0 pixels (for text grouping)

## Error Handling

The system includes comprehensive error handling:

- **Missing API Key**: Graceful fallback to processing without vision analysis
- **Image Extraction Errors**: Continues processing other images
- **Vision Model Errors**: Records error in chunk but continues processing
- **File I/O Errors**: Detailed error reporting and recovery

## Performance Considerations

- **Vision Model Calls**: Each image requires an API call, which can be time-consuming and costly
- **Memory Usage**: Large PDFs with many images may require significant memory
- **Processing Time**: Vision analysis adds substantial processing time
- **API Limits**: OpenAI API has rate limits that may affect batch processing

## Examples

See `example_visual_usage.py` for comprehensive examples including:
- Basic visual processing without vision model
- Full processing with vision model integration
- Testing vision analyzer independently
- Error handling and troubleshooting

## Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Create config file: `kg_agents/openai_config.json` with your API key
   - Set the `OPENAI_API_KEY` environment variable
   - Or pass the key directly: `VisionAnalyzer(api_key="your-key")`

2. **"No PDF files found"**
   - Ensure PDFs are in the correct folder: `subprocess_raw_data/raw/`
   - Check file permissions and extensions (.pdf)

3. **"PyMuPDF import error"**
   - Install PyMuPDF: `pip install pymupdf>=1.23.0`

4. **Vision model errors**
   - Check API key validity and account credits
   - Verify internet connection
   - Check OpenAI service status

### Debug Mode

Enable debug output by setting environment variable:
```bash
export DEBUG_VISUAL_PROCESSING=1
python visual_main.py
```

## Integration with Main Pipeline

The visual processing system is designed to run in parallel with the main PDF processing pipeline:

- **Independent Operation**: Does not interfere with existing functionality
- **Separate Output**: Uses different folder structure to avoid conflicts
- **Shared Dependencies**: Uses same base libraries (tiktoken, NLTK) where applicable
- **Complementary Results**: Can be used alongside regular text chunks for comprehensive analysis

## Future Enhancements

Potential improvements for the visual processing system:

- **OCR Integration**: Extract text from images using OCR
- **Table Detection**: Specialized handling for tables and structured data
- **Multi-modal Chunking**: Combine visual and text chunks intelligently
- **Batch Vision Processing**: Optimize API calls for better performance
- **Custom Vision Models**: Support for other vision model providers
