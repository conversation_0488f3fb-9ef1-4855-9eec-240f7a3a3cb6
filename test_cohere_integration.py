"""
Test script for Cohere integration.

This script creates test data and validates the Cohere integration workflow.
"""

import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

def create_test_config(api_key_placeholder: str = "your_cohere_api_key_here") -> str:
    """Create a test configuration file."""
    config_data = {
        "neo4j": {
            "uri": "bolt://localhost:7687",
            "username": "neo4j",
            "password": "test_password",
            "database": "neo4j"
        },
        "openai": {
            "api_key": "your_openai_api_key_here",
            "embedding_model": "text-embedding-3-small",
            "chat_model": "gpt-3.5-turbo"
        },
        "cohere": {
            "api_key": api_key_placeholder,
            "environment": "TEST",
            "client_name": "GD302",
            "embedding_model": "embed-english-v3.0",
            "input_type": "search_document",
            "embedding_types": ["float"],
            "truncate": "END"
        },
        "processing": {
            "max_workers": 4,
            "batch_size": 10,
            "save_intermediate": True
        }
    }
    
    config_path = "neo4j_ingestion/test_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"✅ Test configuration created: {config_path}")
    return config_path


def create_test_staging_data(staging_path: str = "data_processing_steps/staging") -> List[str]:
    """Create test staging data with sample JSON chunks."""
    staging_dir = Path(staging_path)
    staging_dir.mkdir(parents=True, exist_ok=True)
    
    # Sample texts for testing
    sample_texts = [
        {
            "text": "Machine learning is a subset of artificial intelligence that focuses on algorithms that can learn from data. It enables computers to make predictions and decisions without being explicitly programmed for every scenario.",
            "metadata": {"source": "ml_intro", "topic": "machine_learning"}
        },
        {
            "text": "Natural language processing (NLP) is a field of AI that deals with the interaction between computers and human language. It involves teaching machines to understand, interpret, and generate human language in a valuable way.",
            "metadata": {"source": "nlp_intro", "topic": "natural_language_processing"}
        },
        {
            "text": "Deep learning is a subset of machine learning that uses neural networks with multiple layers. These networks can automatically learn hierarchical representations of data, making them particularly effective for tasks like image recognition and language understanding.",
            "metadata": {"source": "dl_intro", "topic": "deep_learning"}
        },
        {
            "text": "Data science combines statistics, programming, and domain expertise to extract insights from data. It involves collecting, cleaning, analyzing, and visualizing data to support decision-making processes in various industries.",
            "metadata": {"source": "ds_intro", "topic": "data_science"}
        },
        {
            "text": "Computer vision is a field of AI that enables machines to interpret and understand visual information from the world. It involves developing algorithms that can process, analyze, and make decisions based on visual data such as images and videos.",
            "metadata": {"source": "cv_intro", "topic": "computer_vision"}
        }
    ]
    
    created_folders = []
    
    # Create test folders
    for i in range(2):  # Create 2 test folders
        folder_name = f"chunks_{i}_test_document_{i}"
        folder_path = staging_dir / folder_name
        folder_path.mkdir(exist_ok=True)
        
        # Create JSON files in each folder
        chunks_per_folder = 3 if i == 0 else 2  # First folder has 3 chunks, second has 2
        for j in range(chunks_per_folder):
            chunk_data = sample_texts[(i * 3 + j) % len(sample_texts)].copy()
            chunk_data["chunk_id"] = f"chunk_{j}"
            chunk_data["chunk_index"] = j
            chunk_data["total_chunks"] = chunks_per_folder
            
            chunk_file = folder_path / f"chunk_{j}.json"
            with open(chunk_file, 'w', encoding='utf-8') as f:
                json.dump(chunk_data, f, indent=2, ensure_ascii=False)
        
        created_folders.append(str(folder_path))
        print(f"✅ Created test folder: {folder_name} with {chunks_per_folder} chunks")
    
    print(f"✅ Test staging data created in: {staging_path}")
    return created_folders


def test_configuration_loading():
    """Test configuration loading."""
    print("\n" + "="*50)
    print("🧪 Testing Configuration Loading")
    print("="*50)
    
    try:
        from neo4j_ingestion.config import ConfigManager
        
        # Test with template config
        config_manager = ConfigManager("neo4j_ingestion/config_template.json")
        print("✅ Configuration template loaded successfully")
        
        # Test Cohere config access
        cohere_config = config_manager.cohere_config
        print(f"✅ Cohere config loaded:")
        print(f"   Environment: {cohere_config.environment}")
        print(f"   Client Name: {cohere_config.client_name}")
        print(f"   Model: {cohere_config.embedding_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False


def test_data_processor():
    """Test the data processor without actual API calls."""
    print("\n" + "="*50)
    print("🧪 Testing Data Processor")
    print("="*50)
    
    try:
        from neo4j_ingestion.data_processor import DataProcessor
        from neo4j_ingestion.config import ConfigManager
        
        # Create test config (without real API keys)
        config_path = create_test_config()
        config_manager = ConfigManager(config_path)
        
        # Create test data
        staging_folders = create_test_staging_data()
        
        # Initialize data processor
        data_processor = DataProcessor(config_manager, "data_processing_steps/staging")
        
        # Test folder scanning
        indexed_folders = data_processor.scan_staging_folders()
        print(f"✅ Found {len(indexed_folders)} folders")
        
        for index, name, path in indexed_folders:
            print(f"   [{index}] {name}")
            
            # Test JSON file reading
            chunks_data = data_processor.read_json_files_from_folder(path)
            print(f"       📄 {len(chunks_data)} JSON files")
            
            # Test text extraction
            valid_chunks = data_processor.extract_text_fields(chunks_data)
            print(f"       ✅ {len(valid_chunks)} valid text chunks")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processor test failed: {e}")
        return False


def test_workflow_dry_run():
    """Test the workflow in dry-run mode."""
    print("\n" + "="*50)
    print("🧪 Testing Workflow (Dry Run)")
    print("="*50)
    
    try:
        # Import the workflow function
        import sys
        sys.path.append('.')
        from cohere_workflow import run_workflow
        
        # Create test config and data
        config_path = create_test_config()
        create_test_staging_data()
        
        # Run dry run
        results = run_workflow(
            config_file=config_path,
            staging_path="data_processing_steps/staging",
            embedding_provider="cohere",
            use_batch_embeddings=True,
            dry_run=True
        )
        
        print(f"✅ Dry run completed with status: {results['status']}")
        
        if results['status'] == 'dry_run_success':
            print("✅ All checks passed!")
            staging_info = results['staging_info']
            print(f"   📊 Found {staging_info['total_folders']} folders")
            print(f"   🔧 Embedding provider: {results['embedding_provider']}")
            print(f"   📦 Batch embeddings: {results['use_batch_embeddings']}")
            return True
        else:
            print(f"❌ Dry run failed: {results['message']}")
            return False
        
    except Exception as e:
        print(f"❌ Workflow dry run test failed: {e}")
        return False


def test_cohere_embedding_service():
    """Test the Cohere embedding service structure (without API calls)."""
    print("\n" + "="*50)
    print("🧪 Testing Cohere Embedding Service Structure")
    print("="*50)
    
    try:
        from neo4j_ingestion.cohere_embedding_service import CohereEmbeddingService
        from neo4j_ingestion.config import ConfigManager
        
        # Create test config
        config_path = create_test_config()
        config_manager = ConfigManager(config_path)
        
        # Test service initialization (will fail without real API key, but structure should be OK)
        try:
            service = CohereEmbeddingService(config_manager)
            print("⚠️  Service initialized (API key validation will fail without real key)")
        except Exception as e:
            print(f"⚠️  Service initialization failed as expected (no real API key): {e}")
        
        # Test JSONL creation with sample data
        sample_chunks = [
            {"text": "Sample text 1", "chunk_id": "test_1", "metadata": {}},
            {"text": "Sample text 2", "chunk_id": "test_2", "metadata": {}}
        ]
        
        # Test JSONL creation method exists and works
        if hasattr(CohereEmbeddingService, 'create_jsonl_dataset'):
            print("✅ create_jsonl_dataset method exists")
        
        if hasattr(CohereEmbeddingService, 'process_chunks_to_embeddings'):
            print("✅ process_chunks_to_embeddings method exists")
        
        print("✅ Cohere embedding service structure is valid")
        return True
        
    except Exception as e:
        print(f"❌ Cohere embedding service test failed: {e}")
        return False


def cleanup_test_data():
    """Clean up test data and configuration."""
    print("\n" + "="*50)
    print("🧹 Cleaning Up Test Data")
    print("="*50)
    
    try:
        # Remove test staging data
        staging_path = Path("data_processing_steps/staging")
        if staging_path.exists():
            for item in staging_path.iterdir():
                if item.is_dir() and "test_document" in item.name:
                    shutil.rmtree(item)
                    print(f"🗑️  Removed test folder: {item.name}")
        
        # Remove test config
        test_config_path = Path("neo4j_ingestion/test_config.json")
        if test_config_path.exists():
            test_config_path.unlink()
            print(f"🗑️  Removed test config: {test_config_path}")
        
        print("✅ Cleanup completed")
        
    except Exception as e:
        print(f"⚠️  Cleanup failed: {e}")


def main():
    """Run all tests."""
    print("🚀 Starting Cohere Integration Tests")
    print("="*60)
    
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Data Processor", test_data_processor),
        ("Cohere Embedding Service", test_cohere_embedding_service),
        ("Workflow Dry Run", test_workflow_dry_run),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{status} - {test_name}")
        if passed_test:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cohere integration is ready.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    # Cleanup
    cleanup_test_data()
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
