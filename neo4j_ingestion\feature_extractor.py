"""Feature extraction module for document analysis and Neo4j ingestion."""

import os
import json
import re
import logging
from typing import List, Dict, Any, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from .config import ConfigManager


class FeatureExtractor:
    """Handles feature extraction from document chunks including embeddings and key phrases."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.openai_client = config_manager.get_openai_client()
        self.embedding_model = config_manager.openai_config.embedding_model
        self.chat_model = config_manager.openai_config.chat_model
        
    def get_embedding(self, text: str) -> List[float]:
        """Generates an embedding vector for a given text using OpenAI."""
        try:
            text = text.replace("\n", " ")
            response = self.openai_client.embeddings.create(
                input=[text], 
                model=self.embedding_model
            )
            return response.data[0].embedding
        except Exception as e:
            logging.error(f"Error generating embedding: {e}")
            return []
    
    def extract_key_phrases(self, text: str) -> List[str]:
        """Extracts key phrases or entities from text using OpenAI's chat model."""
        prompt = f"""
        From the text provided below, extract the top 5-7 most important key phrases, concepts, or named entities.
        Return the result as a JSON object with a "phrases" key containing a list of strings.
        Example: {{"phrases": ["Machine Learning", "Large Language Models", "Data Science"]}}
        
        Text:
        ---
        {text}
        ---
        """
        
        try:
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
                response_format={"type": "json_object"}
            )
            
            result_json = json.loads(response.choices[0].message.content)
            return result_json.get("phrases", [])
            
        except (json.JSONDecodeError, KeyError) as e:
            logging.error(f"Error parsing phrases from OpenAI response: {e}")
            return []
        except Exception as e:
            logging.error(f"Error extracting key phrases: {e}")
            return []
    
    def tokenize_text(self, text: str) -> Dict[str, int]:
        """A simple tokenizer to get words and their frequencies."""
        tokens = re.findall(r'\b\w+\b', text.lower())
        frequency = {}
        for token in tokens:
            frequency[token] = frequency.get(token, 0) + 1
        return frequency
    
    def process_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Dict[str, Any]:
        """Process a single chunk to extract all features."""
        text = chunk_data.get('text', '')
        if not text:
            logging.warning(f"No text found in chunk {chunk_id}")
            return {}
        
        logging.info(f"Processing chunk {chunk_id}")
        
        try:
            # Extract all features
            embedding = self.get_embedding(text)
            key_phrases = self.extract_key_phrases(text)
            tokens_freq = self.tokenize_text(text)
            
            result = {
                'chunk_id': chunk_id,
                'text': text,
                'embedding': embedding,
                'key_phrases': key_phrases,
                'tokens_frequency': tokens_freq,
                'metadata': chunk_data.get('metadata', {})
            }
            
            logging.info(f"Successfully processed chunk {chunk_id}")
            return result
            
        except Exception as e:
            logging.error(f"Error processing chunk {chunk_id}: {e}")
            return {}


class StagingProcessor:
    """Processes staging folders and extracts features from JSON chunks."""
    
    def __init__(self, staging_path: str = "staging"):
        self.staging_path = Path(staging_path)
        self.feature_extractor = None
        
    def initialize_extractor(self, config_manager: ConfigManager):
        """Initialize the feature extractor with configuration."""
        self.feature_extractor = FeatureExtractor(config_manager)
    
    def discover_staging_folders(self) -> List[Tuple[int, Path]]:
        """Discover and index staging folders."""
        folders = []
        if not self.staging_path.exists():
            logging.warning(f"Staging path {self.staging_path} does not exist")
            return folders
        
        for idx, folder in enumerate(self.staging_path.iterdir()):
            if folder.is_dir() and folder.name.startswith('chunks_'):
                folders.append((idx, folder))
                logging.info(f"Found staging folder {idx}: {folder.name}")
        
        return sorted(folders, key=lambda x: x[1].name)
    
    def load_chunk_files(self, folder_path: Path) -> List[Tuple[str, Dict[str, Any]]]:
        """Load all chunk JSON files from a folder."""
        chunks = []
        
        for chunk_file in folder_path.glob('chunk_*.json'):
            try:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunk_data = json.load(f)
                    chunk_id = f"{folder_path.name}_{chunk_file.stem}"
                    chunks.append((chunk_id, chunk_data))
            except Exception as e:
                logging.error(f"Error loading chunk file {chunk_file}: {e}")
        
        return chunks
    
    def process_staging_folders(self, max_workers: int = 4) -> List[Dict[str, Any]]:
        """Process all staging folders and extract features from chunks."""
        if not self.feature_extractor:
            raise ValueError("Feature extractor not initialized. Call initialize_extractor() first.")
        
        all_processed_chunks = []
        folders = self.discover_staging_folders()
        
        if not folders:
            logging.warning("No staging folders found")
            return all_processed_chunks
        
        for folder_idx, folder_path in folders:
            logging.info(f"Processing folder {folder_idx}: {folder_path.name}")
            chunks = self.load_chunk_files(folder_path)
            
            if not chunks:
                logging.warning(f"No chunks found in folder {folder_path.name}")
                continue
            
            # Process chunks in parallel
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_chunk = {
                    executor.submit(self.feature_extractor.process_chunk, chunk_data, chunk_id): chunk_id
                    for chunk_id, chunk_data in chunks
                }
                
                for future in as_completed(future_to_chunk):
                    chunk_id = future_to_chunk[future]
                    try:
                        result = future.result()
                        if result:
                            result['folder_index'] = folder_idx
                            result['folder_name'] = folder_path.name
                            all_processed_chunks.append(result)
                    except Exception as e:
                        logging.error(f"Error processing chunk {chunk_id}: {e}")
        
        logging.info(f"Processed {len(all_processed_chunks)} chunks total")
        return all_processed_chunks
    
    def save_processed_chunks(self, processed_chunks: List[Dict[str, Any]], 
                            output_file: str = "neo4j_ingestion/processed_chunks.json"):
        """Save processed chunks to a JSON file."""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(processed_chunks, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Saved {len(processed_chunks)} processed chunks to {output_file}")
        except Exception as e:
            logging.error(f"Error saving processed chunks: {e}")
            raise
