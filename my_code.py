def sanitize_db_name_for_cypher(db_name: str) -> str:
    """
    Sanitizes a database name for safe use in Cypher CREATE DATABASE statements.
    Neo4j database names should typically start with a letter and contain only
    alphanumeric characters and underscores. This function aims to enforce that.
    It also wraps the name in backticks.
    """
    if not db_name:
        raise ValueError("Database name cannot be empty.")
    
    # Replace problematic characters with underscores
    s = re.sub(r'[^a-zA-Z0-9_]', '_', db_name)
    
    # Ensure it starts with a letter (prepend 'db_' if not)
    if not s[0].isalpha():
        s = 'db_' + s
        
    # Remove leading/trailing underscores and multiple underscores
    s = '_'.join(filter(None, s.split('_')))
    
    if not s: # Should not happen if original db_name was not empty
        return "`default_db_name`" # Fallback just in case
        
    return f"`{s}`" # Wrap in backticks

class Neo4jConnector:
    def __init__(self, database_name: str = None):
        self.uri = config_manager.get_neo4j_uri()
        self.user = config_manager.get_neo4j_user()
        self.password = config_manager.get_neo4j_password()
        
        self._system_db_name = config_manager.get_config("NEO4J_SYSTEM_DATABASE", "system") # Usually 'system' for admin tasks
        self._default_db_name_config = config_manager.get_neo4j_default_database() # e.g., 'neo4j'

        self._active_database = database_name if database_name else self._default_db_name_config
        self._driver = None
        self._last_error_creating_db = None # Store error message for fallback logic

        if not all([self.uri, self.user, self.password]):
            raise ValueError("Neo4j credentials not found.")
        
        self._connect()

    def _connect(self):
        try:
            if self._driver: self._driver.close()
            self._driver = GraphDatabase.driver(self.uri, auth=basic_auth(self.user, self.password))
            self._driver.verify_connectivity()
            logger.info(f"Neo4j driver connected to DBMS at {self.uri}.")
            logger.info(f"Neo4jConnector targeting database: '{self._active_database}' for general operations.")
        except neo4j_exceptions.ServiceUnavailable: # ... (error handling) ...
            logger.error(f"Failed to connect to Neo4j at {self.uri}: Service Unavailable.")
            raise
        except neo4j_exceptions.AuthError:
            logger.error(f"Failed to connect to Neo4j at {self.uri}: Authentication Error.")
            raise
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j or verify connectivity: {e}")
            self._driver = None
            raise

    def close(self):
        if self._driver is not None:
            self._driver.close()
            self._driver = None # Ensure driver is reset
            logger.info("Neo4j driver connection closed.")

    def get_session(self, database_name: str = None):
        """Gets a session, defaulting to the connector's active database."""
        if self._driver is None:
            logger.error("Neo4j driver not initialized. Attempting to reconnect.")
            self._connect()
            if self._driver is None: raise ConnectionError("Neo4j driver is not available.")
        db_to_use = database_name if database_name else self._active_database
        return self._driver.session(database=db_to_use)

    def execute_query(self, query, parameters=None, database: str = None):
        """Executes a query on the specified database, or the connector's active database."""
        records = []
        summary = None
        db_for_this_query = database if database else self._active_database
        try:
            with self.get_session(database_name=db_for_this_query) as session:
                logger.debug(f"Executing Cypher query on DB '{db_for_this_query}': {query} with params: {parameters}")
                result = session.run(query, parameters)
                records = [record for record in result]
                summary = result.consume()
                logger.debug(f"Query finished on DB '{db_for_this_query}'. Counters: {summary.counters}")
        except neo4j_exceptions.ServiceUnavailable: # ... (error handling) ...
            logger.error(f"Neo4j service became unavailable during query execution on DB '{db_for_this_query}'.")
            self._connect()
            raise 
        except neo4j_exceptions.Neo4jError as e: # ... (error handling for DatabaseNotFound etc.) ...
            target_db_for_error = database if database else self._active_database
            if "Neo.ClientError.Database.DatabaseNotFound" in str(e):
                 logger.error(f"Database '{target_db_for_error}' not found during query execution. Error: {e}")
            else:
                logger.error(f"Error executing Cypher query on DB '{target_db_for_error}': {e}\nQuery: {query}\nParams: {parameters}")
            raise
        except Exception as e:
            target_db_for_error = database if database else self._active_database
            logger.error(f"Unexpected error executing Cypher query on DB '{target_db_for_error}': {e}\nQuery: {query}\nParams: {parameters}")
            raise
        return records, summary

    def check_database_exists(self, db_name_to_check: str) -> bool:
        """Checks if a database exists. Requires Neo4j Enterprise & appropriate permissions."""
        if db_name_to_check == self._default_db_name_config and self._default_db_name_config == "neo4j": # 'neo4j' DB usually exists
             # A more robust check even for 'neo4j' db if it's not the system db
            try:
                with self.get_session(database_name=db_name_to_check) as session: # Try to connect to it
                    session.run("RETURN 1 LIMIT 1") # Simple query to test connection
                return True
            except neo4j_exceptions.Neo4jError as e:
                if "Neo.ClientError.Database.DatabaseNotFound" in str(e):
                    return False
                logger.warning(f"Error when trying to connect to '{db_name_to_check}' to check existence: {e}")
                return False # Assume not accessible if other error
            except Exception:
                return False


        logger.debug(f"Checking existence of database: '{db_name_to_check}'")
        try:
            # SHOW DATABASE <db_name> is more direct if supported and db_name is simple
            # Or, SHOW DATABASES YIELD name, and filter in Python
            # The YIELD clause structure can vary.
            # Let's try the most common `SHOW DATABASES YIELD name` and filter.
            records, _ = self.execute_query("SHOW DATABASES YIELD name", 
                                            database=self._system_db_name) # Admin commands on system DB
            for record in records:
                if record["name"] == db_name_to_check:
                    logger.debug(f"Database '{db_name_to_check}' found in SHOW DATABASES.")
                    return True
            logger.debug(f"Database '{db_name_to_check}' NOT found in SHOW DATABASES.")
            return False
        except neo4j_exceptions.ClientError as e: # Catch specific client errors
            # This can happen if 'SHOW DATABASES' is not allowed, or syntax error with a particular Neo4j version
            logger.warning(f"ClientError when running SHOW DATABASES to check for '{db_name_to_check}': {e}. This might indicate a permission or Neo4j version issue for this command.")
            # Fallback: try to open a session to it.
            try:
                with self.get_session(database_name=db_name_to_check) as session:
                    session.run("RETURN 1 LIMIT 1") # Dummy query to see if it connects
                logger.info(f"Successfully connected to database '{db_name_to_check}' as a fallback existence check.")
                return True
            except neo4j_exceptions.Neo4jError as ne:
                if "Neo.ClientError.Database.DatabaseNotFound" in str(ne):
                    logger.info(f"Database '{db_name_to_check}' not found (via connection attempt).")
                    return False
                logger.warning(f"Neo4jError when trying to connect to '{db_name_to_check}' as fallback: {ne}")
            except Exception as ex_inner:
                 logger.warning(f"Unexpected error when trying to connect to '{db_name_to_check}' as fallback: {ex_inner}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking database '{db_name_to_check}' existence: {e}")
            return False


    def create_database_if_not_exists(self, db_name_to_create: str) -> bool:
        self._last_error_creating_db = None
        if self.check_database_exists(db_name_to_create):
            logger.info(f"Database '{db_name_to_create}' already exists.")
            return True
        
        # Validate the name format: starts with letter, then alphanumeric, dots, or dashes.
        # Based on Neo4j error message: "Use simple ascii characters, numbers, dots and dashes"
        # Note: Underscores are NOT mentioned in the error message and are rejected by Neo4j
        if not re.match(r"^[a-zA-Z][a-zA-Z0-9.-]*$", db_name_to_create):
            error_msg = (f"Database name '{db_name_to_create}' is not valid for CREATE DATABASE. "
                         "It must start with a letter and contain only alphanumeric characters, dots, and dashes.")
            logger.error(error_msg)
            self._last_error_creating_db = error_msg
            return False

        # The db_name_to_create needs to be quoted if it contains dashes or other special characters
        # Use backticks to quote the database name for CREATE DATABASE statement
        create_db_query = f"CREATE DATABASE `{db_name_to_create}` IF NOT EXISTS"
        logger.info(f"Attempting to execute: {create_db_query} on system database '{self._system_db_name}'")
        
        try:
            with self._driver.session(database=self._system_db_name) as session:
                 session.run(create_db_query) 
            
            # ... (rest of the waiting logic as before) ...
            max_retries = 20; wait_time = 3
            for i in range(max_retries):
                # ... (wait and check) ...
                if self.check_database_exists(db_name_to_create):
                    try:
                        with self.get_session(database_name=db_name_to_create) as new_db_session:
                            new_db_session.run("RETURN 1")
                        logger.info(f"Database '{db_name_to_create}' created and is online.")
                        return True
                    except Exception as e_connect:
                        logger.warning(f"Database '{db_name_to_create}' found, but connection test failed: {e_connect}. Retrying wait...")
            
            logger.error(f"Database '{db_name_to_create}' did not become available after {max_retries * wait_time}s.")
            self._last_error_creating_db = f"DB '{db_name_to_create}' did not come online."
            return False
        except neo4j_exceptions.ClientError as e:
            self._last_error_creating_db = str(e)
            # Check if the actual error from Neo4j complains about illegal characters.
            # Neo4j only allows: simple ascii characters, numbers, dots and dashes
            if "contains illegal characters" in str(e).lower():
                logger.error(f"Neo4j rejected database name '{db_name_to_create}' due to illegal characters. Error: {e}")
            elif "Unsupported administration command" in str(e) or "available in Neo4j Enterprise Edition" in str(e):
                logger.error(f"Failed to create database '{db_name_to_create}'. Command requires Neo4j Enterprise Edition. Error: {e}")
            elif "already exists" in str(e).lower():
                logger.info(f"Database '{db_name_to_create}' reported as already existing during creation attempt: {e}")
                return True 
            else:
                logger.error(f"ClientError creating database '{db_name_to_create}': {e}")
            return False
        except Exception as e:
            self._last_error_creating_db = str(e)
            logger.error(f"Unexpected error creating database '{db_name_to_create}': {e}")
            return False

        
    def set_active_database(self, db_name: str):
        """Sets the active database for subsequent operations by this connector instance."""
        logger.info(f"Setting active database for Neo4jConnector to: '{db_name}'")
        self._active_database = db_name
        # No need to reconnect driver, sessions will use the new _active_database
        # self._connect() # Re-establish driver and verify new active DB (optional, can be done lazily by get_session)

    def ensure_constraints_indexes(self, database: str = None):
         # Example: Ensure unique constraint on Entity ID
         # Add more specific constraints and indexes as needed
         # Make sure queries are run against the correct database:
        db_to_use = database if database else self._active_database
        default_chunk_label = config_manager.get_config('NEO4J_VECTOR_NODE_LABEL', 'Chunk')
        constraint_and_index_queries = [
            f"CREATE CONSTRAINT IF NOT EXISTS FOR (c:{config_manager.get_config('NEO4J_VECTOR_NODE_LABEL', 'Chunk')}) REQUIRE c.id IS UNIQUE",
            "CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.type)",
            f"CREATE INDEX IF NOT EXISTS FOR (c:{config_manager.get_config('NEO4J_VECTOR_NODE_LABEL', 'Chunk')}) ON (c.document_id)",
            "CREATE CONSTRAINT IF NOT EXISTS FOR (e:Entity) REQUIRE e.id IS UNIQUE",
            "CREATE CONSTRAINT IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            "CREATE CONSTRAINT IF NOT EXISTS FOR (c:Chunk) REQUIRE c.id IS UNIQUE",
            f"CREATE CONSTRAINT IF NOT EXISTS FOR (c:{default_chunk_label}) REQUIRE c.id IS UNIQUE", # Example for Chunk
            # Add indexes for frequently queried properties
            "CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.name)",
            "CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.type)",
            # Example for vector index (specific to Neo4j version and setup)
            # This might be handled by Neo4jVector class later
        ]

        logger.info(f"Ensuring constraints and indexes on database: '{db_to_use}'...")

        for individual_query_string in constraint_and_index_queries: # Iterate through the defined list
            try:
                # Pass the current query string from the list
                self.execute_query(individual_query_string, database=db_to_use) 
                logger.info(f"Successfully executed setup query on DB '{db_to_use}': {individual_query_string}")
            except Exception as e:
                logger.warning(f"Could not execute setup query '{individual_query_string}' on DB '{db_to_use}': {e} (Constraint/Index might already exist or other issue)")
        
        logger.info(f"Finished ensuring constraints and indexes on database: '{db_to_use}'.")


    # Add more specific methods for your data ingestion, e.g.,

    def ingest_entities(self, entities: List[Dict[str, Any]], document_id: str = None):
        """
        Ingests a list of extracted entities into Neo4j.
        Merges entities based on their name and type.
        """
        if not entities:
            logger.debug("No entities provided to ingest_entities.")
            return

        tx_count = 0
        # Use try-with-resources for the session, using the active database
        try:
            with self.get_session() as session:
                for entity_data in entities:
                    name = entity_data.get("name")
                    entity_type = entity_data.get("type", "UnknownEntity")
                    properties = entity_data.get("properties", {})
                    source_chunk_id = entity_data.get("_source_chunk_id") # From extractor
                    doc_id_from_entity = entity_data.get("_document_id") # From extractor

                    if not name:
                        logger.warning(f"Skipping entity with no name: {entity_data}")
                        continue
                    
                    # Consolidate document ID
                    final_doc_id = doc_id_from_entity or document_id

                    # Prepare properties for Cypher, excluding name and type which are in MERGE clause
                    cypher_props = dict(properties) # Start with base properties
                    if source_chunk_id: cypher_props["_source_chunk_id"] = source_chunk_id
                    if final_doc_id: cypher_props["_document_id"] = final_doc_id
                    # Add other fixed properties or update existing ones

                    # MERGE on (name, type) node key constraint is assumed by this query
                    # If you use a different unique ID (e.g., 'id'), adjust MERGE accordingly.
                    cypher = """
                    MERGE (e:Entity {name: $name, type: $entity_type})
                    ON CREATE SET e += $props, e.created_at = timestamp()
                    ON MATCH SET e += $props, 
                                 e.last_seen_at = timestamp(),
                                 e.source_chunk_ids = CASE 
                                     WHEN $source_chunk_id IS NULL THEN e.source_chunk_ids 
                                     WHEN e.source_chunk_ids IS NULL THEN [$source_chunk_id]
                                     WHEN NOT $source_chunk_id IN e.source_chunk_ids THEN e.source_chunk_ids + $source_chunk_id
                                     ELSE e.source_chunk_ids 
                                 END
                    RETURN id(e) as node_id
                    """
                    
                    params = {
                        "name": name,
                        "entity_type": entity_type,
                        "props": cypher_props, # Pass all additional/updated properties here
                        "source_chunk_id": source_chunk_id # For the CASE statement logic
                    }
                    
                    try:
                        session.run(cypher, params)
                        tx_count +=1
                    except Exception as e_inner:
                        logger.error(f"Failed to ingest entity '{name}' ({entity_type}): {e_inner} \nQuery: {cypher} \nParams: {params}", exc_info=True)
        except Exception as e_outer:
            logger.error(f"Neo4j session error during entity ingestion: {e_outer}", exc_info=True)
            # Potentially re-raise if this is critical
        
        if tx_count > 0:
            logger.info(f"Ingested/Merged {tx_count} entities into Neo4j.")
        else:
            logger.info("No entities were ingested/merged in this call to ingest_entities.")


    def ingest_relationships(self, relationships: List[Dict[str, Any]], document_id: str = None):
        """
        Ingests a list of extracted relationships into Neo4j.
        """
        if not relationships:
            logger.debug("No relationships provided to ingest_relationships.")
            return

        tx_count = 0
        try:
            with self.get_session() as session:
                for rel_data in relationships:
                    source_name = rel_data.get("source_entity_name")
                    # Assuming source_type might be available from entity extraction if needed for precise matching
                    # source_type = rel_data.get("source_entity_type", "Entity") 
                    target_name = rel_data.get("target_entity_name")
                    # target_type = rel_data.get("target_entity_type", "Entity")
                    rel_type_raw = rel_data.get("relationship_type", "RELATED_TO")
                    rel_type = rel_type_raw.replace(" ", "_").replace("-", "_").upper() # Sanitize rel type
                    
                    properties = rel_data.get("properties", {})
                    source_chunk_id = rel_data.get("_source_chunk_id")
                    doc_id_from_rel = rel_data.get("_document_id")

                    if not all([source_name, target_name, rel_type]):
                        logger.warning(f"Skipping relationship with missing fields (source, target, or type): {rel_data}")
                        continue
                    
                    final_doc_id = doc_id_from_rel or document_id
                    if source_chunk_id: properties["_source_chunk_id"] = source_chunk_id
                    if final_doc_id: properties["_document_id"] = final_doc_id
                    
                    # Match source and target entities by name and type (assuming Entity label and (name,type) key)
                    # Adjust MATCH if your entity identification is different
                    cypher = f"""
                    MATCH (source:Entity {{name: $source_name}}) 
                    MATCH (target:Entity {{name: $target_name}})
                    MERGE (source)-[r:`{rel_type}`]->(target)
                    ON CREATE SET r = $props, r.created_at = timestamp()
                    ON MATCH SET r += $props, r.last_seen_at = timestamp() 
                    RETURN id(r) as rel_id
                    """
                    # Note: Using backticks for rel_type ``{rel_type}`` allows for special characters if any remain after sanitization,
                    # but good sanitization is key.
                    
                    params = {
                        "source_name": source_name,
                        # "source_type": source_type, # Add if matching on type too
                        "target_name": target_name,
                        # "target_type": target_type, # Add if matching on type too
                        "props": properties
                    }
                    try:
                        session.run(cypher, params)
                        tx_count += 1
                    except Exception as e_inner:
                        logger.error(f"Failed to ingest relationship {source_name}-[{rel_type}]->{target_name}: {e_inner} \nQuery: {cypher} \nParams: {params}", exc_info=True)
        except Exception as e_outer:
            logger.error(f"Neo4j session error during relationship ingestion: {e_outer}", exc_info=True)

        if tx_count > 0:
            logger.info(f"Ingested/Merged {tx_count} relationships into Neo4j.")
        else:
            logger.info("No relationships were ingested/merged in this call to ingest_relationships.")