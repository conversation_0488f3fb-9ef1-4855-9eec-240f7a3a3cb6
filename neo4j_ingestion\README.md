# Neo4j Document Ingestion Module

This module provides an independent system for ingesting processed document chunks into a Neo4j graph database with OpenAI-powered feature extraction.

## Overview

The Neo4j ingestion module processes document chunks from the staging folders, extracts features using OpenAI APIs, and constructs a knowledge graph in Neo4j. The system includes:

1. **Configuration Management**: Handles OpenAI and Neo4j credentials with error handling
2. **Feature Extraction**: Generates embeddings and extracts key phrases from document chunks
3. **Graph Construction**: Builds a comprehensive knowledge graph in Neo4j
4. **Pipeline Orchestration**: Coordinates the entire ingestion process

## Features

- **Parallel Processing**: Multi-threaded feature extraction for improved performance
- **Robust Error Handling**: Comprehensive error handling and logging
- **Flexible Configuration**: Support for both file-based and environment variable configuration
- **Batch Processing**: Efficient batch operations for Neo4j ingestion
- **Graph Statistics**: Built-in monitoring and statistics collection
- **Modular Design**: Independent components that can be used separately

## Installation

1. Install the required dependencies:
```bash
pip install -r neo4j_ingestion/requirements.txt
```

2. Set up your configuration file by copying the template:
```bash
cp neo4j_ingestion/config_template.json neo4j_ingestion/config.json
```

3. Edit the configuration file with your credentials:
```json
{
  "neo4j": {
    "uri": "bolt://localhost:7687",
    "username": "neo4j",
    "password": "your_neo4j_password",
    "database": "neo4j"
  },
  "openai": {
    "api_key": "your_openai_api_key_here",
    "embedding_model": "text-embedding-3-small",
    "chat_model": "gpt-3.5-turbo"
  }
}
```

## Usage

### Command Line Interface

Run the complete ingestion pipeline:
```bash
python -m neo4j_ingestion.main_ingestion
```

Run with custom parameters:
```bash
python -m neo4j_ingestion.main_ingestion --staging ./staging --workers 8 --batch-size 20
```

Run only feature extraction:
```bash
python -m neo4j_ingestion.main_ingestion --extract-only
```

Run only graph construction (requires previously extracted features):
```bash
python -m neo4j_ingestion.main_ingestion --graph-only
```

### Programmatic Usage

```python
from neo4j_ingestion.main_ingestion import Neo4jIngestionPipeline

# Create and run pipeline
pipeline = Neo4jIngestionPipeline(
    config_file="neo4j_ingestion/config.json",
    staging_path="staging"
)

results = pipeline.run_full_pipeline(
    max_workers=4,
    batch_size=10,
    save_intermediate=True
)

print(f"Pipeline results: {results}")
```

## Graph Schema

The system creates the following node types and relationships:

### Node Types
- **Document**: Represents individual document chunks
- **Folder**: Represents staging folders containing chunks
- **Token**: Individual words with frequency information
- **Phrase**: Key phrases extracted from documents
- **Embedding**: Vector embeddings for semantic search

### Relationships
- **CONTAINS**: Folder → Document
- **HAS_EMBEDDING**: Document → Embedding
- **CONTAINS_TOKEN**: Document → Token (with frequency property)
- **MENTIONS_PHRASE**: Document → Phrase
- **CO_OCCURS**: Phrase → Phrase (phrases appearing together)

## Configuration Options

### Environment Variables
You can also configure the system using environment variables:

```bash
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USERNAME="neo4j"
export NEO4J_PASSWORD="your_password"
export NEO4J_DATABASE="neo4j"
export OPENAI_API_KEY="your_openai_key"
export OPENAI_EMBEDDING_MODEL="text-embedding-3-small"
export OPENAI_CHAT_MODEL="gpt-3.5-turbo"
```

### Command Line Arguments
- `--config`: Path to configuration file
- `--staging`: Path to staging directory (default: "staging")
- `--workers`: Number of worker threads (default: 4)
- `--batch-size`: Batch size for graph operations (default: 10)
- `--no-save-intermediate`: Don't save intermediate results
- `--extract-only`: Only run feature extraction
- `--graph-only`: Only run graph construction

## Logging

The system generates detailed logs in `neo4j_ingestion/ingestion.log` and outputs to console. Log levels can be configured in the setup.

## Error Handling

The system includes comprehensive error handling for:
- OpenAI API connection and rate limiting
- Neo4j database connection issues
- File I/O operations
- JSON parsing errors
- Network timeouts

## Performance Considerations

- **Parallel Processing**: Use `--workers` to control thread count based on your system
- **Batch Size**: Adjust `--batch-size` for optimal Neo4j performance
- **Memory Usage**: Large embeddings may require memory management for large datasets
- **API Rate Limits**: OpenAI API calls are subject to rate limiting

## Troubleshooting

1. **Connection Issues**: Verify Neo4j is running and credentials are correct
2. **API Errors**: Check OpenAI API key and quota
3. **Memory Issues**: Reduce batch size or worker count
4. **Empty Results**: Verify staging folder structure and JSON format

## Integration

This module is designed to work with the existing document processing pipeline:
1. PDFs are processed and chunked into the `staging` folder
2. This module reads the staging folders and processes the chunks
3. Results are stored in Neo4j for further analysis and querying
