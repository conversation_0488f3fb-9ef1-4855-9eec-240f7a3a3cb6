"""Graph construction module for Neo4j ingestion."""

import logging
from typing import List, Dict, Any, Optional
from neo4j import Session
from .config import ConfigManager


class GraphConstructor:
    """Handles the construction of document graphs in Neo4j."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.driver = config_manager.get_neo4j_driver()
        self.database = config_manager.neo4j_config.database
        
    def create_indexes(self):
        """Create necessary indexes for better performance."""
        indexes = [
            "CREATE INDEX document_id_index IF NOT EXISTS FOR (d:Document) ON (d.id)",
            "CREATE INDEX token_value_index IF NOT EXISTS FOR (t:Token) ON (t.value)",
            "CREATE INDEX phrase_value_index IF NOT EXISTS FOR (p:Phrase) ON (p.value)",
            "CREATE INDEX folder_name_index IF NOT EXISTS FOR (f:Folder) ON (f.name)"
        ]
        
        with self.driver.session(database=self.database) as session:
            for index_query in indexes:
                try:
                    session.run(index_query)
                    logging.info(f"Created index: {index_query}")
                except Exception as e:
                    logging.warning(f"Index creation failed or already exists: {e}")
    
    def add_document_to_graph(self, chunk_data: Dict[str, Any]) -> bool:
        """
        Processes a document chunk and adds its corresponding nodes and relationships to the graph.
        """
        try:
            doc_id = chunk_data['chunk_id']
            doc_text = chunk_data['text']
            embedding_vector = chunk_data['embedding']
            key_phrases = chunk_data['key_phrases']
            tokens_freq = chunk_data['tokens_frequency']
            folder_name = chunk_data.get('folder_name', 'unknown')
            folder_index = chunk_data.get('folder_index', 0)
            
            logging.info(f"Adding document {doc_id} to graph")
            
            with self.driver.session(database=self.database) as session:
                # Create or update folder node
                session.run(
                    """
                    MERGE (f:Folder {name: $folder_name})
                    SET f.index = $folder_index
                    """,
                    folder_name=folder_name,
                    folder_index=folder_index
                )
                
                # Create document node
                session.run(
                    """
                    MERGE (d:Document {id: $id})
                    SET d.text = $text, d.chunk_id = $id
                    """,
                    id=doc_id,
                    text=doc_text
                )
                
                # Connect document to folder
                session.run(
                    """
                    MATCH (d:Document {id: $doc_id})
                    MATCH (f:Folder {name: $folder_name})
                    MERGE (f)-[:CONTAINS]->(d)
                    """,
                    doc_id=doc_id,
                    folder_name=folder_name
                )
                
                # Create embedding node and connect it to the document
                if embedding_vector:
                    session.run(
                        """
                        MATCH (d:Document {id: $doc_id})
                        CREATE (e:Embedding {vector: $vector})
                        MERGE (d)-[:HAS_EMBEDDING]->(e)
                        """,
                        doc_id=doc_id,
                        vector=embedding_vector
                    )
                
                # Create token nodes and connect them
                for token, freq in tokens_freq.items():
                    session.run(
                        """
                        MATCH (d:Document {id: $doc_id})
                        MERGE (t:Token {value: $token_value})
                        MERGE (d)-[r:CONTAINS_TOKEN]->(t)
                        SET r.frequency = $freq
                        """,
                        doc_id=doc_id,
                        token_value=token,
                        freq=freq
                    )
                
                # Create phrase nodes and connect them
                for phrase in key_phrases:
                    if phrase:  # Skip empty phrases
                        session.run(
                            """
                            MATCH (d:Document {id: $doc_id})
                            MERGE (p:Phrase {value: $phrase_value})
                            MERGE (d)-[:MENTIONS_PHRASE]->(p)
                            """,
                            doc_id=doc_id,
                            phrase_value=phrase.lower()
                        )
                
                # Create relationships between phrases that appear in the same document
                if len(key_phrases) > 1:
                    for i, phrase1 in enumerate(key_phrases):
                        for phrase2 in key_phrases[i+1:]:
                            if phrase1 and phrase2:
                                session.run(
                                    """
                                    MATCH (p1:Phrase {value: $phrase1})
                                    MATCH (p2:Phrase {value: $phrase2})
                                    MERGE (p1)-[r:CO_OCCURS]->(p2)
                                    SET r.count = COALESCE(r.count, 0) + 1
                                    """,
                                    phrase1=phrase1.lower(),
                                    phrase2=phrase2.lower()
                                )
            
            logging.info(f"Successfully added document {doc_id} to graph")
            return True
            
        except Exception as e:
            logging.error(f"Error adding document {chunk_data.get('chunk_id', 'unknown')} to graph: {e}")
            return False
    
    def batch_add_documents(self, chunks_data: List[Dict[str, Any]], batch_size: int = 10) -> Dict[str, int]:
        """
        Add multiple documents to the graph in batches for better performance.
        """
        results = {"success": 0, "failed": 0}
        
        for i in range(0, len(chunks_data), batch_size):
            batch = chunks_data[i:i + batch_size]
            logging.info(f"Processing batch {i//batch_size + 1} ({len(batch)} documents)")
            
            for chunk_data in batch:
                if self.add_document_to_graph(chunk_data):
                    results["success"] += 1
                else:
                    results["failed"] += 1
        
        logging.info(f"Batch processing complete. Success: {results['success']}, Failed: {results['failed']}")
        return results
    
    def get_graph_statistics(self) -> Dict[str, int]:
        """Get basic statistics about the graph."""
        stats = {}
        
        queries = {
            "documents": "MATCH (d:Document) RETURN count(d) as count",
            "folders": "MATCH (f:Folder) RETURN count(f) as count",
            "tokens": "MATCH (t:Token) RETURN count(t) as count",
            "phrases": "MATCH (p:Phrase) RETURN count(p) as count",
            "embeddings": "MATCH (e:Embedding) RETURN count(e) as count",
            "relationships": "MATCH ()-[r]->() RETURN count(r) as count"
        }
        
        try:
            with self.driver.session(database=self.database) as session:
                for stat_name, query in queries.items():
                    result = session.run(query)
                    stats[stat_name] = result.single()["count"]
            
            logging.info(f"Graph statistics: {stats}")
            return stats
            
        except Exception as e:
            logging.error(f"Error getting graph statistics: {e}")
            return {}
    
    def clear_graph(self, confirm: bool = False):
        """Clear all nodes and relationships from the graph. Use with caution!"""
        if not confirm:
            logging.warning("Graph clear operation requires confirmation. Set confirm=True to proceed.")
            return
        
        try:
            with self.driver.session(database=self.database) as session:
                session.run("MATCH (n) DETACH DELETE n")
            logging.info("Graph cleared successfully")
        except Exception as e:
            logging.error(f"Error clearing graph: {e}")
            raise
    
    def close(self):
        """Close the database connection."""
        if self.driver:
            self.driver.close()
