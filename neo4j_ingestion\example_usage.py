"""Example usage of the Neo4j ingestion module."""

import logging
from pathlib import Path
from neo4j_ingestion.main_ingestion import Neo4jIngestionPipeline
from neo4j_ingestion.config import setup_logging


def example_full_pipeline():
    """Example of running the complete ingestion pipeline."""
    print("=== Running Full Neo4j Ingestion Pipeline ===")
    
    # Setup logging
    setup_logging("INFO")
    
    # Create pipeline instance
    pipeline = Neo4jIngestionPipeline(
        config_file="neo4j_ingestion/config.json",  # Make sure this file exists
        staging_path="staging"  # Path to your staging folder
    )
    
    try:
        # Run the complete pipeline
        results = pipeline.run_full_pipeline(
            max_workers=4,          # Number of parallel workers
            batch_size=10,          # Batch size for Neo4j operations
            save_intermediate=True  # Save processed chunks to JSON
        )
        
        print(f"Pipeline Results: {results}")
        
        if results["status"] == "success":
            print(f"✅ Successfully processed {results['chunks_processed']} chunks")
            print(f"📊 Graph Statistics: {results['graph_statistics']}")
        else:
            print(f"❌ Pipeline failed: {results.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Pipeline execution failed: {e}")
        logging.error(f"Pipeline execution failed: {e}")


def example_feature_extraction_only():
    """Example of running only the feature extraction step."""
    print("=== Running Feature Extraction Only ===")
    
    setup_logging("INFO")
    
    pipeline = Neo4jIngestionPipeline(
        config_file="neo4j_ingestion/config.json",
        staging_path="staging"
    )
    
    try:
        # Initialize the pipeline
        pipeline.initialize()
        
        # Run only feature extraction
        processed_chunks = pipeline.run_feature_extraction(
            max_workers=4,
            save_intermediate=True
        )
        
        print(f"✅ Feature extraction complete. Processed {len(processed_chunks)} chunks")
        print("💾 Results saved to neo4j_ingestion/processed_chunks.json")
        
        # Show sample of extracted features
        if processed_chunks:
            sample = processed_chunks[0]
            print(f"\n📄 Sample chunk: {sample['chunk_id']}")
            print(f"🔑 Key phrases: {sample['key_phrases'][:3]}...")  # Show first 3 phrases
            print(f"📊 Token count: {len(sample['tokens_frequency'])}")
            print(f"🧮 Embedding dimensions: {len(sample['embedding'])}")
            
    except Exception as e:
        print(f"❌ Feature extraction failed: {e}")
        logging.error(f"Feature extraction failed: {e}")
    finally:
        pipeline.cleanup()


def example_graph_construction_only():
    """Example of running only the graph construction step."""
    print("=== Running Graph Construction Only ===")
    
    setup_logging("INFO")
    
    # Check if processed chunks file exists
    processed_chunks_file = Path("neo4j_ingestion/processed_chunks.json")
    if not processed_chunks_file.exists():
        print("❌ No processed chunks file found. Run feature extraction first.")
        return
    
    # Load processed chunks
    import json
    with open(processed_chunks_file, 'r') as f:
        processed_chunks = json.load(f)
    
    print(f"📂 Loaded {len(processed_chunks)} processed chunks")
    
    pipeline = Neo4jIngestionPipeline(
        config_file="neo4j_ingestion/config.json",
        staging_path="staging"
    )
    
    try:
        # Initialize the pipeline
        pipeline.initialize()
        
        # Run only graph construction
        results = pipeline.run_graph_construction(
            processed_chunks,
            batch_size=10
        )
        
        print(f"✅ Graph construction complete")
        print(f"📊 Processing Results: {results['processing_results']}")
        print(f"📈 Graph Statistics: {results['graph_statistics']}")
        
    except Exception as e:
        print(f"❌ Graph construction failed: {e}")
        logging.error(f"Graph construction failed: {e}")
    finally:
        pipeline.cleanup()


def example_configuration_check():
    """Example of checking configuration and connections."""
    print("=== Configuration and Connection Check ===")
    
    setup_logging("INFO")
    
    try:
        from neo4j_ingestion.config import ConfigManager
        
        # Test configuration loading
        config_manager = ConfigManager("neo4j_ingestion/config.json")
        print("✅ Configuration loaded successfully")
        
        # Test OpenAI connection
        openai_client = config_manager.get_openai_client()
        print("✅ OpenAI connection successful")
        
        # Test Neo4j connection
        neo4j_driver = config_manager.get_neo4j_driver()
        print("✅ Neo4j connection successful")
        
        # Test basic operations
        from neo4j_ingestion.graph_constructor import GraphConstructor
        graph_constructor = GraphConstructor(config_manager)
        
        # Get current graph statistics
        stats = graph_constructor.get_graph_statistics()
        print(f"📊 Current Graph Statistics: {stats}")
        
        # Cleanup
        config_manager.close_connections()
        print("✅ All connections tested successfully")
        
    except Exception as e:
        print(f"❌ Configuration check failed: {e}")
        logging.error(f"Configuration check failed: {e}")


def main():
    """Main function to demonstrate different usage patterns."""
    print("Neo4j Ingestion Module - Example Usage")
    print("=" * 50)
    
    # Check if configuration file exists
    config_file = Path("neo4j_ingestion/config.json")
    if not config_file.exists():
        print("⚠️  Configuration file not found!")
        print("Please copy neo4j_ingestion/config_template.json to neo4j_ingestion/config.json")
        print("and update it with your credentials.")
        return
    
    # Check if staging directory exists
    staging_dir = Path("staging")
    if not staging_dir.exists() or not any(staging_dir.iterdir()):
        print("⚠️  Staging directory is empty or doesn't exist!")
        print("Please run the document processing pipeline first to generate chunks.")
        return
    
    print("Choose an example to run:")
    print("1. Configuration and connection check")
    print("2. Feature extraction only")
    print("3. Graph construction only (requires processed chunks)")
    print("4. Full pipeline")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            example_configuration_check()
        elif choice == "2":
            example_feature_extraction_only()
        elif choice == "3":
            example_graph_construction_only()
        elif choice == "4":
            example_full_pipeline()
        else:
            print("Invalid choice. Please run the script again.")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Example execution failed: {e}")


if __name__ == "__main__":
    main()
